import logging
from openai import OpenAI
import requests
from app.tasks.task_manager import MysqlTaskManager
from app.models import MeetingTextRefineTask, beijing_now
from config import cfg
from app.utils import upload_result_to_oss
from app.utils import group_segments_by_speaker_and_size
from datetime import datetime
from typing import List
import json


logger = logging.getLogger(__name__)


# 示例：会议文本优化任务管理器
class MeetingRefineTaskManager(MysqlTaskManager):
    """会议文本优化任务管理器 - 示例实现"""
    
    def __init__(self):
        super().__init__(MeetingTextRefineTask, check_interval=5)
        
       # 获取AI优化的系统提示词
        try:
            response = requests.get(cfg.LLM.REFINE_PROMPT_URL)
            response.encoding = 'utf-8'
            self.refine_system_prompt = response.text
        except Exception as e:
            logger.error(f"获取AI优化提示词失败: {e}")
            self.refine_system_prompt = "请优化以下文本内容"


    def _execute_task_item(self, meeting, db, is_canceled_func):
        """执行会议文本优化任务"""
        try:
            if not meeting.textUrl:
                raise Exception("转写结果不存在")
            
            # 下载转写结果
            logger.info(f"正在下载会议 {meeting.id} 的转写结果...")
            response = requests.get(meeting.textUrl, timeout=30)
            response.raise_for_status()
            
            # 再次检查取消状态
            if is_canceled_func():
                logger.info(f"任务 {meeting.id} 在下载后被取消")
                return
            
            try:
                text_content = json.loads(response.text)
            except json.JSONDecodeError:
                raise Exception("转写结果格式错误")
            
            # 准备分段数据
            segments = []
            for i, segment in enumerate(text_content.get("speaker_segments", [])):
                segments.append({
                    "id": i,
                    "speaker": segment["speaker"],
                    "start_time": segment["start_time"],
                    "end_time": segment["end_time"],
                    "text": segment["text"]
                })
            
            if len(segments) == 0:
                raise Exception("转写结果为空")
            
            # 按speaker和文本长度分组
            groups = group_segments_by_speaker_and_size(segments)
            
            logger.info(f"会议 {meeting.id} 分为 {len(groups)} 个组进行处理")
            
            # 处理每个分组
            results = []
            for i, group in enumerate(groups):
                # 检查取消状态
                if is_canceled_func():
                    logger.info(f"任务 {meeting.id} 在处理第 {i+1} 组时被取消")
                    return
                
                try:
                    result = self._process_single_group(group, is_canceled_func)
                    results.append(result)
                    logger.info(f"会议 {meeting.id} 第 {i+1}/{len(groups)} 组处理完成")
                except Exception as e:
                    logger.error(f"处理组 {i} 失败: {e}")
                    results.append(group)  # 使用原始数据作为fallback
            
            # 最后检查取消状态
            if is_canceled_func():
                logger.info(f"任务 {meeting.id} 在合并结果前被取消")
                return
            
            # 合并结果
            text_content["speaker_segments"] = [
                item for sublist in results for item in sublist
            ]
            
            # 上传结果到OSS
            refined_url = upload_result_to_oss(text_content, meeting.id, "refinetask")
            
            # 更新数据库
            meeting.resultUrl = refined_url
            meeting.status = "success"
            meeting.updatedAt = beijing_now()
            db.commit()
            
            logger.info(f"会议 {meeting.id} AI优化任务完成")
            
        except Exception as e:
            logger.error(f"执行会议优化任务失败: {e}")
            raise

    def _process_single_group(self, group: List[dict], is_canceled_func) -> List[dict]:
        """处理单个分组，支持取消检查"""
        try:
            # 检查取消状态
            if is_canceled_func():
                logger.info("处理分组时检测到取消信号")
                return group
            
            segments = []
            for segment in group:
                segments.append({
                    "id": segment["id"],
                    "text": segment["text"]
                })

            client = OpenAI(
                api_key=cfg.LLM.API_KEY,
                base_url=cfg.LLM.BASE_URL
            )
            
            # 在AI处理前再次检查
            if is_canceled_func():
                logger.info("AI处理前检测到取消信号")
                return group
            
            completion = client.chat.completions.create(
                model="qwen3-235b-a22b",
                messages=[
                    {"role": "system", "content": self.refine_system_prompt},
                    {"role": "user", "content": json.dumps(segments, ensure_ascii=False)},
                ],
                max_tokens=8192,
                stream=False,
                extra_body={"enable_thinking": False},
            )

            message = completion.choices[0].message.content
            
            # AI处理完成后检查
            if is_canceled_func():
                logger.info("AI处理后检测到取消信号")
                return group
            
            # 解析AI返回的结果
            modify_segments = self._extract_json_array(message)
            
            # 验证返回数据
            if not isinstance(modify_segments, list):
                raise ValueError("AI返回的数据不是数组格式")
            
            for i, seg in enumerate(modify_segments):
                if not isinstance(seg, dict):
                    raise ValueError(f"第{i+1}个元素不是对象格式")
                if "id" not in seg or "text" not in seg:
                    raise ValueError(f"第{i+1}个元素缺少必要字段(id或text)")
            
            # 构建修改映射
            modify_segMap = {}
            merged_ids = []
            merged_to = {}
            for modify_seg in modify_segments:
                modify_segMap[modify_seg["id"]] = modify_seg["text"]
                if "merged_ids" in modify_seg:
                    ids = modify_seg["merged_ids"]
                    merged_ids.extend(ids[1:])
                    for merged_id in ids[1:]:
                        merged_to[merged_id] = ids[0]

            # 生成输出结果
            output_segments = []
            group_map = {}
            for seg in group:
                group_map[seg["id"]] = seg

            for seg in group:
                if seg["id"] in merged_ids:
                    toItem = merged_to[seg["id"]]
                    currSeg = group_map[seg["id"]]
                    max_end_time = max(currSeg["end_time"], group_map[toItem]["end_time"])
                    group_map[toItem]["end_time"] = max_end_time
                    continue

                if seg["id"] not in modify_segMap:
                    output_segments.append(seg)
                    continue

                # 创建新的段落对象
                seg["text"] = modify_segMap[seg["id"]]
                output_segments.append(seg)
            
            return output_segments
            
        except Exception as e:
            logger.error(f"处理单个组时出错: {e}")
            return group  # 返回原始数据作为fallback

    def _extract_json_array(self, text: str) -> List[dict]:
        """从AI返回的文本中提取JSON数组"""
        if not text or not isinstance(text, str):
            raise ValueError("返回内容为空或格式错误")
        
        text = text.strip()
        
        # 处理markdown代码块格式
        if "```json" in text:
            start_markers = ["```json", "```JSON"]
            end_marker = "```"
            
            for start_marker in start_markers:
                if start_marker in text:
                    start_idx = text.find(start_marker) + len(start_marker)
                    end_idx = text.find(end_marker, start_idx)
                    if end_idx != -1:
                        text = text[start_idx:end_idx].strip()
                        break
        
        # 处理其他代码块格式
        elif "```" in text:
            parts = text.split("```")
            if len(parts) >= 3:
                text = parts[1].strip()
                lines = text.split('\n')
                if lines and lines[0].lower() in ['json', 'javascript', 'js']:
                    text = '\n'.join(lines[1:]).strip()
        
        # 查找JSON数组的开始和结束位置
        start_idx = -1
        for i, char in enumerate(text):
            if char == '[':
                start_idx = i
                break
        
        if start_idx == -1:
            for i, char in enumerate(text):
                if char == '{':
                    start_idx = i
                    break
        
        if start_idx == -1:
            raise ValueError("未找到JSON数据的开始位置")
        
        # 查找对应的结束位置
        bracket_count = 0
        start_char = text[start_idx]
        end_char = ']' if start_char == '[' else '}'
        
        for i in range(start_idx, len(text)):
            char = text[i]
            if char == start_char:
                bracket_count += 1
            elif char == end_char:
                bracket_count -= 1
                if bracket_count == 0:
                    end_idx = i
                    break
        else:
            raise ValueError("未找到JSON数据的结束位置")
        
        # 提取并解析JSON
        json_str = text[start_idx:end_idx + 1]
        
        try:
            parsed_data = json.loads(json_str)
            
            if isinstance(parsed_data, list):
                return parsed_data
            elif isinstance(parsed_data, dict):
                return [parsed_data]
            else:
                raise ValueError("解析出的数据不是对象或数组")
                
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON解析失败: {str(e)}")

# 可以用这个替换原来的全局实例
refine_task_manager = MeetingRefineTaskManager()