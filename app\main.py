# main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2PasswordBearer
from app.mysql import engine
from app.models import Base
from fastapi.openapi.docs import (
    get_swagger_ui_html,
)

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
import traceback
import logging


logging.basicConfig(level=logging.INFO)  # 设置日志级别为DEBUG

# 设置日志
logger = logging.getLogger(__name__)

# 创建数据库表
Base.metadata.create_all(bind=engine)

# 创建FastAPI应用
app = FastAPI(
    prefix="/v1",
    title="Meeting API",
    openapi_url="/v1/openapi.json",
    docs_url=None,  # 禁用默认文档路由
    redoc_url="/v1/redoc",

    openapi_tags=[
        {"name": "auth", "description": "认证相关接口"},
        {"name": "会议管理", "description": "会议管理相关接口"},
    ]
)

# 配置允许的源
origins = [
    "http://*************:5173",
    "http://***************:5173",
    "http://localhost:5173",
    "http://127.0.0.1:3000",
    "http://**************:3000",
    "http://localhost:8848"
    # 添加其他需要的前端源
]

# 挂载静态文件
app.mount("/v1/static", StaticFiles(directory="static"), name="static")

@app.on_event("startup")
async def startup_event():
    """应用启动时的事件处理"""
    logger.info("🚀 启动 Meeting API 应用...")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的事件处理"""
    logger.info("🛑 正在关闭 Meeting API 应用...")
            
# 自定义文档路由
@app.get("/v1/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """
    Custom Swagger UI endpoint that serves the API documentation interface.
    This endpoint is excluded from the OpenAPI schema itself.
    
    Returns:
        HTML: Rendered Swagger UI interface with custom configuration.
    """
    return get_swagger_ui_html(
        openapi_url="/v1/openapi.json",
        title="API Docs",
        swagger_js_url="/v1/static/swagger-ui-bundle.js",
        swagger_css_url="/v1/static/swagger-ui.css",
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    print("httpException",exc.status_code, exc.detail)
    #这里如果状态码是401，则返回401
    if  exc.status_code == 401:
        return JSONResponse(
            status_code=401,
            content={"code": 401, "message": "Unauthorized", "data": None},
        )
    
    return JSONResponse(
        status_code=200,
        content={
            "code": exc.status_code,
            "message": exc.detail,
            "data": None,
        },
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    # 打印错误日志（可选）
    traceback.print_exc()
    
    #这里如果状态码是401，则返回401
    if isinstance(exc, HTTPException) and exc.status_code == 401:
        return JSONResponse(
            status_code=401,
            content={"code": 401, "message": "Unauthorized", "data": None},
        )
    
    return JSONResponse(
        status_code=200,
        content={
            "code": 500,
            "message": "Internal Server Error",
            "data": None,
        },
    )

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
    expose_headers=["*"]  # 暴露所有头部
)

# 导入路由器
from app.routers import auth,meeting,oss, refine, callback, export, summary

# 注册路由器
app.include_router(auth.router)
# app.include_router(chat.router)   
# app.include_router(conversations.router)
# app.include_router(messages.router)
# app.include_router(summary.router)
app.include_router(meeting.router)
app.include_router(oss.router)
app.include_router(refine.router)   
app.include_router(callback.router) 
app.include_router(export.router) 
app.include_router(summary.router)
