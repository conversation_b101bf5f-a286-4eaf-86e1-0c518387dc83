import asyncio
import json
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from openai import OpenAI
import requests
import logging

from app.models import Meeting, beijing_now
from app.mysql import get_db
from app.tasks.executor import upload_result_to_oss
from config import cfg

logger = logging.getLogger(__name__)

class SummaryTaskManager:
    """会议纪要总结任务管理器"""
    
    def __init__(self):
        self.is_running = False
        self.worker_thread = None
        self.lock = threading.Lock()
        self.running_tasks: Dict[int, threading.Thread] = {}  # meeting_id -> thread
        self.task_status: Dict[int, dict] = {}  # meeting_id -> status info
        self.max_workers = 3  # 最大并发任务数
        
        # 获取会议纪要总结的系统提示词
        self.summary_system_prompt = """
你是一个专业的会议纪要总结助手。请根据提供的会议转写内容，生成结构化的会议纪要总结。
要求：
1. 确保输出是有效的markdown格式
2. 保持内容详尽，不要遗漏任何重要信息,避免冗余
3. 重点突出决策和行动项
4. 用中文输出
5. 会议要点要精炼，不要冗余

输出请按照以下Markdown格式输出会议纪要：
```markdown
# 会议主题/标题

## 会议概述
(2-3句话的会议整体概述)

## 关键要点
- 关键要点1
- 关键要点2
- 关键要点3

## 会议决策
- 决策1
- 决策2

## 行动项
| 任务描述 | 负责人 | 截止时间 |
|----------|--------|----------|
| 任务1 | (如有提到) | (如有提到) |
| 任务2 | (如有提到) | (如有提到) |

## 与会人员及贡献
**发言人1** (角色，如能推断):
- 主要贡献/观点1
- 主要贡献/观点2

**发言人2** (角色，如能推断):
- 主要贡献/观点1

## 后续步骤
- 后续步骤1
- 后续步骤2
"""

    def start(self):
        """启动任务管理器"""
        with self.lock:
            if self.is_running:
                return
            
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
            logger.info("会议纪要总结任务管理器已启动")
            
        # 恢复未完成的任务
        self._recover_pending_tasks()

    def stop(self):
        """停止任务管理器"""
        with self.lock:
            if not self.is_running:
                return
            
            self.is_running = False
            
            # 等待所有运行中的任务完成
            for meeting_id, thread in list(self.running_tasks.items()):
                if thread.is_alive():
                    thread.join(timeout=5)  # 最多等待5秒
            
            logger.info("会议纪要总结任务管理器已停止")

    def submit_summary_task(self, meeting_id: int) -> bool:
        """提交会议纪要总结任务"""
        with self.lock:
            if meeting_id in self.running_tasks:
                logger.warning(f"会议 {meeting_id} 的总结任务已在运行中")
                return False
            
            if len(self.running_tasks) >= self.max_workers:
                logger.warning(f"已达到最大并发任务数 {self.max_workers}，会议 {meeting_id} 将排队等待")
                return False
            
            # 更新数据库状态
            try:
                db = next(get_db())
                meeting = db.query(Meeting).filter(Meeting.id == meeting_id).first()
                if not meeting:
                    logger.error(f"会议 {meeting_id} 不存在")
                    return False
                
                # 检查是否有文本内容可以总结
                if not meeting.refinedTextUrl and not meeting.textUrl:
                    logger.error(f"会议 {meeting_id} 没有可用的文本内容进行总结")
                    return False
                
                meeting.status = "summarizing"
                meeting.updatedAt = beijing_now()
                db.commit()
                db.close()
                
                # 创建任务线程
                task_thread = threading.Thread(
                    target=self._execute_summary_task,
                    args=(meeting_id,),
                    daemon=True
                )
                
                self.running_tasks[meeting_id] = task_thread
                self.task_status[meeting_id] = {
                    "status": "starting",
                    "progress": 0,
                    "started_at": beijing_now().isoformat(),
                    "error": None
                }
                
                task_thread.start()
                logger.info(f"已提交会议 {meeting_id} 的纪要总结任务")
                return True
                
            except Exception as e:
                logger.error(f"提交纪要总结任务失败: {e}")
                return False

    def get_task_status(self, meeting_id: int) -> Optional[dict]:
        """获取任务状态"""
        with self.lock:
            return self.task_status.get(meeting_id)

    def cancel_task(self, meeting_id: int) -> bool:
        """取消任务"""
        with self.lock:
            if meeting_id not in self.running_tasks:
                return False
            
            # 更新状态为取消
            if meeting_id in self.task_status:
                self.task_status[meeting_id]["status"] = "canceled"
            
            # 更新数据库
            try:
                db = next(get_db())
                meeting = db.query(Meeting).filter(Meeting.id == meeting_id).first()
                if meeting:
                    # 根据之前的状态回退
                    if meeting.refinedTextUrl:
                        meeting.status = "refined"
                    else:
                        meeting.status = "texted"
                    meeting.updatedAt = beijing_now()
                    db.commit()
                db.close()
            except Exception as e:
                logger.error(f"取消任务时更新数据库失败: {e}")
            
            return True

    def _worker_loop(self):
        """工作线程主循环"""
        while self.is_running:
            try:
                # 清理已完成的任务
                self._cleanup_finished_tasks()
                
                # 检查是否有排队的任务需要启动
                self._start_pending_tasks()
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                logger.error(f"总结任务管理器工作循环出错: {e}")
                time.sleep(10)

    def _cleanup_finished_tasks(self):
        """清理已完成的任务"""
        with self.lock:
            finished_tasks = []
            for meeting_id, thread in self.running_tasks.items():
                if not thread.is_alive():
                    finished_tasks.append(meeting_id)
            
            for meeting_id in finished_tasks:
                del self.running_tasks[meeting_id]
                # 保留状态信息一段时间，供查询使用
                if meeting_id in self.task_status:
                    status = self.task_status[meeting_id]
                    if status.get("status") not in ["completed", "failed", "canceled"]:
                        status["status"] = "completed"

    def _start_pending_tasks(self):
        """启动排队中的任务"""
        if len(self.running_tasks) >= self.max_workers:
            return
            
        try:
            db = next(get_db())
            # 查找状态为summarizing但没有在运行的任务
            pending_meetings = db.query(Meeting).filter(
                Meeting.status == "summarizing"
            ).limit(self.max_workers - len(self.running_tasks)).all()
            
            for meeting in pending_meetings:
                if meeting.id not in self.running_tasks:
                    self.submit_summary_task(meeting.id)
            
            # 查找状态为refined但没有总结的任务
            pending_meetings = db.query(Meeting).filter(
                Meeting.status == "refined"
            ).limit(self.max_workers - len(self.running_tasks)).all()

            for meeting in pending_meetings:
                if meeting.id not in self.running_tasks:
                    self.submit_summary_task(meeting.id)

            db.close()
        except Exception as e:
            logger.error(f"启动排队任务失败: {e}")

    def _recover_pending_tasks(self):
        """恢复未完成的任务"""
        try:
            db = next(get_db())
            pending_meetings = db.query(Meeting).filter(
                Meeting.status == "summarizing"
            ).all()
            
            logger.info(f"发现 {len(pending_meetings)} 个未完成的纪要总结任务，开始恢复")
            
            for meeting in pending_meetings:
                self.submit_summary_task(meeting.id)
            
            db.close()
        except Exception as e:
            logger.error(f"恢复未完成任务失败: {e}")

    def _execute_summary_task(self, meeting_id: int):
        """执行会议纪要总结任务"""
        db = None
        try:
            logger.info(f"开始执行会议 {meeting_id} 的纪要总结任务")
            
            # 更新任务状态
            self.task_status[meeting_id]["status"] = "running"
            self.task_status[meeting_id]["progress"] = 10
            
            db = next(get_db())
            meeting = db.query(Meeting).filter(Meeting.id == meeting_id).first()
            
            if not meeting:
                raise Exception(f"会议 {meeting_id} 不存在")
            
            # 优先使用优化后的文本，如果没有则使用原始文本
            text_url = meeting.refinedTextUrl or meeting.textUrl
            if not text_url:
                raise Exception("没有可用的文本内容进行总结")
            
            # 下载文本内容
            response = requests.get(text_url, timeout=30)
            response.raise_for_status()
            
            self.task_status[meeting_id]["progress"] = 30
            
            try:
                text_content = json.loads(response.text)
            except json.JSONDecodeError:
                raise Exception("文本内容格式错误")
            
            # 提取会议内容
            meeting_text = self._extract_meeting_content(text_content)
            
            if not meeting_text:
                raise Exception("会议内容为空")
            
            self.task_status[meeting_id]["progress"] = 50
            
            # 使用LLM生成会议纪要总结
            summary_result = self._generate_summary_with_llm(meeting_text)
            
            self.task_status[meeting_id]["progress"] = 80
              
            # 上传结果到OSS
            summary_url = self._upload_summary_text_to_oss(summary_result, meeting_id)
            
            # 更新数据库
            meeting.summaryUrl = summary_url
            meeting.status = "summarized"
            meeting.updatedAt = beijing_now()
            db.commit()
            
            # 更新任务状态
            self.task_status[meeting_id].update({
                "status": "completed",
                "progress": 100,
                "completed_at": beijing_now().isoformat(),
                "summary_url": summary_url
            })
            
            logger.info(f"会议 {meeting_id} 纪要总结任务完成")
            
        except Exception as e:
            logger.error(f"执行纪要总结任务失败: {e}")
            
            # 更新任务状态为失败
            if meeting_id in self.task_status:
                self.task_status[meeting_id].update({
                    "status": "failed",
                    "error": str(e),
                    "failed_at": beijing_now().isoformat()
                })
            
            # 更新数据库状态
            if db:
                try:
                    meeting = db.query(Meeting).filter(Meeting.id == meeting_id).first()
                    if meeting:
                        meeting.status = "failed"
                        meeting.updatedAt = beijing_now()
                        db.commit()
                except Exception as db_error:
                    logger.error(f"更新数据库失败状态时出错: {db_error}")
            
        finally:
            if db:
                db.close()
            
            # 从运行中任务列表移除
            with self.lock:
                if meeting_id in self.running_tasks:
                    del self.running_tasks[meeting_id]

    def _extract_meeting_content(self, text_content: dict) -> str:
        """从文本内容中提取会议对话内容"""
        try:
            segments = text_content.get("speaker_segments", [])
            if not segments:
                return ""
            
            # 按时间排序
            # segments.sort(key=lambda x: x.get("start_time", 0))
            
            # 构建对话文本
            meeting_lines = []
            for segment in segments:
                speaker = segment.get("speaker")
                text = segment.get("text", "").strip()
                if text:
                    meeting_lines.append(f"speaker-{speaker}: {text}")
            
            return "\n".join(meeting_lines)
            
        except Exception as e:
            logger.error(f"提取会议内容失败: {e}")
            return ""

    def _generate_summary_with_llm(self, meeting_text: str) -> dict:
        """使用LLM生成会议纪要总结"""
        try:
            client = OpenAI(
                api_key=cfg.LLM.API_KEY,
                base_url=cfg.LLM.BASE_URL
            )
            
            print("llm summary meeting_text:")
            print(meeting_text[0:100])

            completion = client.chat.completions.create(
                model="qwen3-235b-a22b",
                messages=[
                    {"role": "system", "content": self.summary_system_prompt},
                    {"role": "user", "content": meeting_text},
                ],
                max_tokens=8000,
                stream=False,
                temperature=0.3,  # 降低随机性，保证输出稳定
                extra_body={"enable_thinking": False},
            )
            print("llm summary usage:")
            print(completion.usage)
            message = completion.choices[0].message.content
            
            # 解析AI返回的markdown
            summary_data = self._extract_markdown_from_response(message)    
            # summary_data = self._extract_json_from_response(message)
            
            return summary_data
            
        except Exception as e:
            logger.error(f"使用LLM生成总结失败: {e}")
            # 返回基础总结格式
            return {
                "meeting_title": "会议纪要",
                "meeting_summary": "自动总结生成失败",
                "key_points": [],
                "decisions": [],
                "action_items": [],
                "participants": [],
                "next_steps": []
            }
        
    def _extract_markdown_from_response(self, text: str) -> dict:
        """从AI返回的文本中提取markdown数据"""
        if not text or not isinstance(text, str):
            raise ValueError("返回内容为空或格式错误")
        
        # 处理markdown代码块格式
        if "```markdown" in text:
            start_marker = "```markdown"
            end_marker = "```"
            
            start_idx = text.find(start_marker) + len(start_marker) 
            end_idx = text.find(end_marker, start_idx)
            if end_idx != -1:
                text = text[start_idx:end_idx].strip()

        return text

    def _extract_json_from_response(self, text: str) -> dict:
        """从AI返回的文本中提取JSON数据"""
        if not text or not isinstance(text, str):
            raise ValueError("返回内容为空或格式错误")
        
        text = text.strip()
        
        # 处理markdown代码块格式
        if "```json" in text:
            start_markers = ["```json", "```JSON"]
            end_marker = "```"
            
            for start_marker in start_markers:
                if start_marker in text:
                    start_idx = text.find(start_marker) + len(start_marker)
                    end_idx = text.find(end_marker, start_idx)
                    if end_idx != -1:
                        text = text[start_idx:end_idx].strip()
                        break
        
        # 处理其他代码块格式
        elif "```" in text:
            parts = text.split("```")
            if len(parts) >= 3:
                text = parts[1].strip()
                lines = text.split('\n')
                if lines and lines[0].lower() in ['json', 'javascript', 'js']:
                    text = '\n'.join(lines[1:]).strip()
        
        # 查找JSON对象的开始和结束位置
        start_idx = text.find('{')
        if start_idx == -1:
            raise ValueError("未找到JSON数据的开始位置")
        
        # 查找对应的结束位置
        bracket_count = 0
        for i in range(start_idx, len(text)):
            char = text[i]
            if char == '{':
                bracket_count += 1
            elif char == '}':
                bracket_count -= 1
                if bracket_count == 0:
                    end_idx = i
                    break
        else:
            raise ValueError("未找到JSON数据的结束位置")
        
        # 提取并解析JSON
        json_str = text[start_idx:end_idx + 1]
        
        try:
            parsed_data = json.loads(json_str)
            
            if isinstance(parsed_data, dict):
                return parsed_data
            else:
                raise ValueError("解析出的数据不是对象格式")
                
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON解析失败: {str(e)}")

    def _upload_summary_text_to_oss(self, summary_data: str, meeting_id: int) -> str:
        """上传总结结果到OSS"""
        try:
            # 生成文件名和路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"summary_results/{datetime.now().year}/{datetime.now().month:02d}/{meeting_id}_summary_{timestamp}.md"
            
            # 使用现有的上传函数，但修改路径
            import oss2
            auth = oss2.Auth(cfg.OSS.ACCESS_KEY_ID, cfg.OSS.ACCESS_KEY_SECRET)
            bucket = oss2.Bucket(auth, cfg.OSS.ENDPOINT, cfg.OSS.BUCKET_NAME)
       
            # 上传到OSS
            bucket.put_object(filename, summary_data, headers={'Content-Type': 'text/markdown'})
            
            # 生成文件URL
            file_url = f"https://{cfg.OSS.BUCKET_NAME}.{cfg.OSS.ENDPOINT}/{filename}"
            
            logger.info(f"✅ 会议纪要总结已上传到OSS: {file_url}")
            return file_url
            
        except Exception as e:
            logger.error(f"❌ 上传会议纪要总结到OSS失败: {e}")
            raise


    def _upload_summary_to_oss(self, summary_data: dict, meeting_id: int) -> str:
        """上传总结结果到OSS"""
        try:
            # 生成文件名和路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"summary_results/{datetime.now().year}/{datetime.now().month:02d}/{meeting_id}_summary_{timestamp}.md"
            
            # 使用现有的上传函数，但修改路径
            import oss2
            auth = oss2.Auth(cfg.OSS.ACCESS_KEY_ID, cfg.OSS.ACCESS_KEY_SECRET)
            bucket = oss2.Bucket(auth, cfg.OSS.ENDPOINT, cfg.OSS.BUCKET_NAME)
            
            # 将结果转换为JSON字符串
            json_content = json.dumps(summary_data, ensure_ascii=False, indent=2)
            
            # 上传到OSS
            bucket.put_object(filename, json_content, headers={'Content-Type': 'text/markdown'})
            
            # 生成文件URL
            file_url = f"https://{cfg.OSS.BUCKET_NAME}.{cfg.OSS.ENDPOINT}/{filename}"
            
            logger.info(f"✅ 会议纪要总结已上传到OSS: {file_url}")
            return file_url
            
        except Exception as e:
            logger.error(f"❌ 上传会议纪要总结到OSS失败: {e}")
            raise


# 全局任务管理器实例
summary_task_manager = SummaryTaskManager()
