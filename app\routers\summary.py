import json
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, or_
from typing import List, Literal, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime

from app.models import Meeting,MeetingSummeryTask, HttpResult, success, error
import logging
from app.mysql import get_db
from app.tasks.utils import startSummeryTask
from app.utils import copy_file_to_oss, get_current_user

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/api/text-summery", tags=["会议纪要管理"])


# 分页响应模型
class SummeryTaskResponse(BaseModel):
    """
    会议纪要响应模型
    包含任务的详细信息，用于列表展示和详情查看
    """
    id: int = Field(description="任务ID", example=1)
    versionName: str = Field(description="版本名称", example="v1")
    meeting_id: int = Field(description="关联的会议ID", example=123)
    textUrl: str = Field(description="原始文本文件URL")
    refTextUrl: Optional[str] = Field(default=None, description="参考文本文件URL")
    resultUrl: Optional[str] = Field(default=None, description="优化结果文件URL")
    status: str = Field(description="任务状态", example="created")
    createdAt: datetime = Field(description="创建时间")

    name: str = Field(description="会议标题", example="会议标题")
    meeting_at: str = Field(description="会议时间", example="2025-07-11T15:56:16")
    meetType: str = Field(description="会议类型", example="项目会议")
    
    
# 创建任务请求模型
class CreateSummeryTaskRequest(BaseModel):
    """
    创建会议纪要任务的请求模型
    
    用于创建会议文本优化任务，支持提供参考文本来指导优化过程
    """
    meeting_id: int = Field(
        description="会议ID，必须是已存在的会议",
        example=123,
        gt=0
    )
    versionName: Optional[str] = Field(
        default=None,
        description="版本名称，如不提供则自动生成（格式：v1, v2, v3...）",
        example="v1",
        max_length=32
    )
    textUrl: Optional[str] = Field(
        default=None,
        description="会议的文本内容",
        example="https://example.com/reference.txt"
    )

    refTextUrl: Optional[str] = Field(
        default=None,
        description="参考文本URL，用于指导文本优化的参考资料",
        example="https://example.com/reference.txt"
    )

# 创建任务响应模型
class CreateSummeryTaskResponse(BaseModel):
    """
    创建文本优化任务的响应模型
    
    返回新创建任务的详细信息
    """
    id: int = Field(description="任务ID", example=1)
    versionName: str = Field(description="版本名称", example="v1")
    meeting_id: int = Field(description="关联的会议ID", example=123)
    textUrl: str = Field(description="原始文本文件URL")
    refTextUrl: Optional[str] = Field(default=None, description="参考文本文件URL")
    status: str = Field(description="任务状态", example="created")
    createdAt: datetime = Field(description="创建时间")

class PaginatedSummeryTaskResponse(BaseModel):
    """
    分页任务列表响应模型
    
    包含任务列表和分页信息
    """
    list: List[SummeryTaskResponse] = Field(description="任务列表")
    total: int = Field(description="总记录数", example=100)
    page: int = Field(description="当前页码", example=1)
    page_size: int = Field(description="每页数量", example=10)
    total_pages: int = Field(description="总页数", example=10)

# 创建优化任务
@router.post("/create", response_model=HttpResult[CreateSummeryTaskResponse], summary="创建纪要生成任务")
async def create_refine_task(
    task_request: CreateSummeryTaskRequest,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建纪要生成任务
    
    ## 功能说明
    为指定会议创建会议纪要生成任务，可以提供参考文本来指导纪要生成过程。
    
    ## 请求参数说明
    - **meeting_id**: 必须是已存在且有文本内容的会议ID
    - **versionName**: 可选，版本名称，如不提供则自动生成（v1, v2, v3...）
    - **refTextUrl**: 可选，参考文本URL，用于指导文本优化
    - **textUrl**:  必须是会议转录的文本列表。
    
    ## 返回结果
    返回创建成功的任务详细信息，包括任务ID、状态等。
    
    ## 错误情况
    - 404: 会议不存在
    - 400: 会议文本不存在
    - 500: 服务器内部错误
    
    ## 注意事项
    - 系统会自动将文本文件复制到OSS存储
    - 任务创建后状态为 "created"，需要通过其他接口启动任务
    """
    try:
        # 检查会议是否存在
        meeting_exists = db.query(Meeting).filter(Meeting.id == task_request.meeting_id).first()
        if not meeting_exists:
            raise HTTPException(status_code=404, detail="会议不存在")
        
        if task_request.textUrl is None:
            raise HTTPException(status_code=400, detail="会议文本不存在")
        
        if task_request.versionName is None:
           count = db.query(MeetingSummeryTask).filter(MeetingSummeryTask.meeting_id == MeetingSummeryTask.meeting_id).count()
           task_request.versionName = f"v{count+1}" 

        # 拷贝文件到Oss

        ref_text_url = task_request.refTextUrl
        if ref_text_url:
            ref_text_url = copy_file_to_oss(ref_text_url, prefix=f"summerytask/{task_request.meeting_id}")
        
        text_url = copy_file_to_oss(task_request.textUrl, prefix=f"summerytask/{task_request.meeting_id}")

        # 创建新的优化任务
        new_task = MeetingSummeryTask(
            versionName=task_request.versionName,
            meeting_id=task_request.meeting_id,
            textUrl=text_url,
            refTextUrl=ref_text_url,
            status="created"
        )
        db.add(new_task)
        db.commit()
        db.refresh(new_task)

        #触发会议总结流程
        taskResult = startSummeryTask(new_task.textUrl, new_task.refTextUrl, f"{new_task.id}")
        if taskResult["task_id"]:
            new_task.taskId = taskResult["task_id"]
            db.commit()
        else:
            print("submit summery task failed!!!!")
    
        return success(CreateSummeryTaskResponse(
            id=new_task.id,
            versionName=new_task.versionName,
            meeting_id=new_task.meeting_id,
            textUrl=new_task.textUrl,
            refTextUrl=new_task.refTextUrl,
            status=new_task.status,
            createdAt=new_task.createdAt
        ))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建优化任务失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail="创建优化任务失败")

# 删除优化任务
@router.delete("/delete/{task_id}", response_model=HttpResult[str], summary="删除优化任务")
async def delete_refine_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    删除文本优化任务
    """
    try:
        # 查找任务
        task = db.query(MeetingSummeryTask).filter(MeetingSummeryTask.id == task_id).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 检查任务状态，如果正在进行中则不允许删除
        if task.status == "started":
            raise HTTPException(status_code=400, detail="任务正在执行中，无法删除")
        
        # 删除任务
        db.delete(task)
        db.commit()
        
        return success("任务删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除优化任务失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail="删除优化任务失败")

# 获取优化结果
@router.get("/list", response_model=HttpResult[PaginatedSummeryTaskResponse], summary="获取优化任务列表")
async def get_meetings(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
    meeting_id: int = Query(-1, ge=-1, description="会议ID"),
    status: Literal["created","started", "success", "failed"] = Query(None, description="任务状态,可选值: created（已创建）, started(已开始）, success(已完成), failed(失败)"),
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取优化任务列表
    """

    try:
        # 计算偏移量
        offset = (page - 1) * page_size

        base_filter = Meeting.user_id = current_user_id

        # 构建基础查询条件
        if meeting_id > 0:
            base_filter = MeetingSummeryTask.meeting_id == meeting_id

        # 状态查询
        if status and status.strip():  
            base_filter = base_filter & (MeetingSummeryTask.status == status)

        # 查询总数
        total_count_query = db.query(func.count(MeetingSummeryTask.id))\
            .filter(base_filter)
        
        # 打印SQL语句用于调试
        # print("SQL查询语句:", str(total_count_query.statement.compile(compile_kwargs={"literal_binds": True})))
        
        total_count = total_count_query.scalar()

        # 查询会议列表（分页）
        tasks_query = db.query(
            MeetingSummeryTask.id,
            MeetingSummeryTask.versionName,
            MeetingSummeryTask.meeting_id,
            MeetingSummeryTask.textUrl,
            MeetingSummeryTask.refTextUrl,            
            MeetingSummeryTask.resultUrl,
            MeetingSummeryTask.status,
            MeetingSummeryTask.createdAt,
            Meeting.name,
            Meeting.meetingAt,
            Meeting.meetType,
            Meeting.user_id,
        ).join(Meeting, MeetingSummeryTask.meeting_id == Meeting.id)\
         .filter(base_filter)\
         .order_by(MeetingSummeryTask.createdAt.desc())\
         .offset(offset)\
         .limit(page_size)
        
        # 打印分页查询的SQL语句
        # print("分页查询SQL:", str(tasks_query.statement.compile(compile_kwargs={"literal_binds": True})))
        
        tasks = tasks_query.all()

        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size

        # 格式化响应数据
        list = []

        for task in tasks:
            list.append(SummeryTaskResponse(
                id=task.id,
                versionName=task.versionName,
                meeting_id=task.meeting_id,
                textUrl=task.textUrl,
                refTextUrl=task.refTextUrl,
                resultUrl=task.resultUrl,
                status=task.status,
                createdAt=task.createdAt,
                name=task.name,
                meeting_at=task.meetingAt.isoformat() if task.meetingAt else "",
                meetType=task.meetType
            ))

        return success(PaginatedSummeryTaskResponse(
            list=list,
            total=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))

    except Exception as e:
        logger.error(f"获取优化列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取优化表失败")

# 更新会议请求模型
class UpdateTextRequest(BaseModel):
    id: int
    resultUrl: str


@router.post("/update", response_model=HttpResult[bool], summary="更新会议纪要后的文本")
async def update_refined_text(
    req: UpdateTextRequest,
    db: Session = Depends(get_db)
):
    """
    更新优化后的文本
    """
    # 验证会议存在且属于当前用户
    task = db.query(MeetingSummeryTask).filter(
        MeetingSummeryTask.id == req.id
    ).first()

    if not task:
        raise HTTPException(status_code=404, detail="优化文本不存在或无权访问")
    
    task.resultUrl = req.resultUrl
    # 更新修改时间
    task.updatedAt = datetime.utcnow()
    
    db.commit()
    
    return success(True)
