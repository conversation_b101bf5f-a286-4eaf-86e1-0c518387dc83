import re
import zipfile
import logging
import hashlib
import os
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from itertools import groupby
from app.services.docx import DocxParser
from app.models import Meeting
from app.services.summeryMarkdown import comprehensive_markdown_extractor, print_extracted_structure

# 配置日志
logger = logging.getLogger(__name__)


class SummeryDocxParser(DocxParser):
    """会议纪要DOCX解析器"""
    
    def __init__(self):
        super().__init__()
    
    
    def saveSummeryMarkdown2docx(self, markdown_text: str, meeting: Meeting, document_xml: BeautifulSoup) -> BeautifulSoup:
        """
        将会议纪要markdown保存到DOCX文档XML中
        """
        try:
            # 解析markdown
            markdown_parser = comprehensive_markdown_extractor(markdown_text)

            # 打印提取的结构
            print_extracted_structure(markdown_parser)

            # 获取文档结构
            name = meeting.name
            title = markdown_parser['title']
            overview = markdown_parser['overview']
            agenda = markdown_parser['agenda']
            conclusion = markdown_parser['conclusion']
            todo_table = markdown_parser['todo_table']

            logger.info(f"开始处理会议纪要DOCX，会议名称: {name}")

            # 1. 替换标题
            self._replace_text_in_xml(document_xml, "纪要标题", name or title)
            
            # 2. 替换会议基本信息表格中的占位符
            self._replace_text_in_xml(document_xml, "主题内容占位",  name or title)
            
            # 格式化会议时间
            meeting_time = ""
            if meeting.meetingAt:
                meeting_time = meeting.meetingAt.strftime("%Y年%m月%d日 %H:%M")
            self._replace_text_in_xml(document_xml, "会议时间占位", meeting_time)
            
            # 参会人员
            attendees = meeting.users or ""
            self._replace_text_in_xml(document_xml, "参会人占位", attendees)
            
            # 会议地点
            location = meeting.meetingAddress or ""
            self._replace_text_in_xml(document_xml, "会议地点占位", location)
            
            # 会前必读(使用概览内容)
            self._replace_text_in_xml(document_xml, "会前必读占位", meeting.desc)
            
            # 3. 插入议题内容
            # agenda_content = self._format_agenda_content(agenda)
            
            #查文本所在的段落
            para2 = self._find_para_by_text("二级内容", document_xml)
            
            if agenda and agenda.get('topics'):
                prev_para = para2  # 初始化prev_para
                
                for i, topic in enumerate(agenda['topics'], 1):
                    title = topic.get('title', '')
                    
                    # 使用工具函数创建段落
                    new_top_para = self._create_paragraph(title, document_xml)
                    prev_para.insert_after(new_top_para)
                    prev_para = new_top_para

                    # 添加3级内容
                    for item in topic.get('items', []):
                        if item['type'] == 'content':
                            new_para = self._create_paragraph(item['text'], document_xml)
                            prev_para.insert_after(new_para)
                            prev_para = new_para

                        elif item['type'] == 'subtopic':
                            subtopic_text = f"{item['title']}: {item['content']}"
                            new_para = self._create_paragraph(subtopic_text, document_xml)
                            prev_para.insert_after(new_para)
                            prev_para = new_para

                            # 添加详细内容
                            for detail in item.get('details', []):
                                detail_para = self._create_paragraph(detail, document_xml)
                                prev_para.insert_after(detail_para)
                                prev_para = detail_para

            #删除占位内容
            if para2:
                para2.decompose()

            # self._replace_text_in_xml(document_xml, "二级内容", agenda_content)

            # # 处理三级内容 - 如果有子项详情的话
            # sub_content = self._format_agenda_sub_content(agenda)
            # self._replace_text_in_xml(document_xml, "三级内容。", sub_content)
            
            # 4. 替换结论内容
            self._replace_text_in_xml(document_xml, "结论内容", conclusion)
            
            # 5. 替换待办内容
            # todo_content = self._format_todo_content(todo_table)
            # self._replace_text_in_xml(document_xml, "待办内容", todo_content)

            # 待办内容，填入表格
            self._insert_todo_table_rows(document_xml, todo_table)

            logger.info("会议纪要DOCX处理完成")
            return document_xml
            
        except Exception as e:
            logger.error(f"处理会议纪要DOCX时发生错误: {str(e)}")
            raise
    
    def _find_para_by_text(self, text: str, body: BeautifulSoup)-> BeautifulSoup:
        """
        查找包含指定文本的段落
        """
        for para in body.find_all('w:p'):
            if para.text and text in para.text:
                return para
            
        return None

    def _create_paragraph(self, text: str, document_xml: BeautifulSoup) -> BeautifulSoup:
        """
        创建DOCX段落的工具函数
        
        Args:
            text: 段落文本内容
            document_xml: BeautifulSoup文档对象
            
        Returns:
            BeautifulSoup: 创建的段落元素（w:p结构）
        """
        # 创建段落元素
        para = document_xml.new_tag('w:p')
        
        # 创建运行元素
        w_r = document_xml.new_tag('w:r')
        
        # 创建文本元素
        w_t = document_xml.new_tag('w:t')
        w_t.string = text
        
        # 组装结构：w:p -> w:r -> w:t
        w_r.append(w_t)
        para.append(w_r)
        
        return para

    def _replace_text_in_xml(self, document_xml: BeautifulSoup, old_text: str, new_text: str):
        """
        在XML中替换文本内容
        """
        replaced_count = 0
        # 查找所有包含目标文本的w:t标签
        for w_t in document_xml.find_all('w:t'):
            if w_t.string and old_text in w_t.string:
                # 替换文本内容
                new_content = w_t.string.replace(old_text, new_text)
                w_t.string.replace_with(new_content)
                replaced_count += 1
                logger.info(f"替换文本: '{old_text}' -> '{new_text[:50]}{'...' if len(new_text) > 50 else ''}'")
        
        if replaced_count == 0:
            logger.warning(f"未找到占位符: '{old_text}'")
        
        return replaced_count
    
    def _format_agenda_content(self, agenda: Dict) -> str:
        """
        格式化议题内容
        """
        if not agenda or not agenda.get('topics'):
            return "暂无议题内容"
            
        content_lines = []
        
        for i, topic in enumerate(agenda['topics'], 1):
            # 添加主题标题
            content_lines.append(f"{topic['title']}")
            
            # 添加子项内容
            for item in topic.get('items', []):
                if item['type'] == 'content':
                    content_lines.append(f"   • {item['text']}")
                elif item['type'] == 'subtopic':
                    content_lines.append(f"   ▶ {item['title']}: {item['content']}")
                    # 添加详细内容
                    for detail in item.get('details', []):
                        content_lines.append(f"     - {detail}")
            
            content_lines.append("")  # 空行分隔
        
        return "\n".join(content_lines)
    
    def _format_agenda_sub_content(self, agenda: Dict) -> str:
        """
        格式化议题的子项内容（如果有详细内容）
        """
        if not agenda or not agenda.get('topics'):
            return "暂无子项内容"
            
        sub_content_lines = []
        
        for topic in agenda.get('topics', []):
            for item in topic.get('items', []):
                if item['type'] == 'subtopic' and item.get('details'):
                    # 如果有详细内容，展示第一个作为三级内容的示例
                    if item.get('details'):
                        sub_content_lines.append(item['details'][0])
                        break  # 只取第一个作为示例
            if sub_content_lines:  # 如果已经找到内容就退出
                break
                
        return sub_content_lines[0] if sub_content_lines else "议题详细内容"
    
    def _format_todo_content(self, todo_table: Dict) -> str:
        """
        格式化待办事项内容
        """
        if not todo_table or not todo_table.get('rows'):
            return "暂无待办事项"
            
        content_lines = []
        headers = todo_table.get('headers', [])
        
        # 如果有表头，添加表头信息
        if headers:
            content_lines.append(" | ".join(headers))
            content_lines.append(" | ".join(['---'] * len(headers)))
        
        # 添加表格行
        for row in todo_table.get('rows', []):
            if row:  # 确保行不为空
                content_lines.append(" | ".join(row))
        
        return "\n".join(content_lines)
    
    def _create_table_row(self, row_data: List[str], document_xml: BeautifulSoup) -> BeautifulSoup:
        """
        创建表格行XML结构
        
        Args:
            row_data: 行数据列表，包含各个单元格的内容
            document_xml: BeautifulSoup文档对象
            
        Returns:
            BeautifulSoup: 创建的表格行元素（w:tr结构）
        """
        # 生成唯一的paraId
        import uuid
        para_id = str(uuid.uuid4()).replace('-', '').upper()[:8]
        
        # 创建表格行
        tr = document_xml.new_tag('w:tr')
        tr['w14:paraId'] = para_id
        
        # 添加表格属性
        tbl_pr_ex = document_xml.new_tag('w:tblPrEx')
        
        # 表格边框
        tbl_borders = document_xml.new_tag('w:tblBorders')
        for border_name in ['top', 'left', 'bottom', 'right', 'insideH', 'insideV']:
            border = document_xml.new_tag(f'w:{border_name}')
            border['w:val'] = 'single'
            border['w:color'] = 'auto'
            border['w:sz'] = '4'
            border['w:space'] = '0'
            tbl_borders.append(border)
        
        tbl_pr_ex.append(tbl_borders)
        
        # 表格单元格边距
        tbl_cell_mar = document_xml.new_tag('w:tblCellMar')
        left_mar = document_xml.new_tag('w:left')
        left_mar['w:w'] = '108'
        left_mar['w:type'] = 'dxa'
        right_mar = document_xml.new_tag('w:right')
        right_mar['w:w'] = '108'
        right_mar['w:type'] = 'dxa'
        tbl_cell_mar.append(left_mar)
        tbl_cell_mar.append(right_mar)
        
        tbl_pr_ex.append(tbl_cell_mar)
        tr.append(tbl_pr_ex)
        
        # 列宽设置（与提供的模板对应）
        column_widths = ['5566', '1525', '1775']
        
        # 创建表格单元格
        for i, cell_data in enumerate(row_data):
            # 确保不超过列数
            if i >= len(column_widths):
                break
                
            tc = document_xml.new_tag('w:tc')
            
            # 单元格属性
            tc_pr = document_xml.new_tag('w:tcPr')
            tc_w = document_xml.new_tag('w:tcW')
            tc_w['w:w'] = column_widths[i]
            tc_w['w:type'] = 'dxa'
            tc_pr.append(tc_w)
            tc.append(tc_pr)
            
            # 单元格段落
            cell_para_id = str(uuid.uuid4()).replace('-', '').upper()[:8]
            p = document_xml.new_tag('w:p')
            p['w14:paraId'] = cell_para_id
            
            # 段落属性
            p_pr = document_xml.new_tag('w:pPr')
            
            # 缩进
            ind = document_xml.new_tag('w:ind')
            ind['w:left'] = '0'
            ind['w:leftChars'] = '0'
            ind['w:firstLine'] = '0'
            ind['w:firstLineChars'] = '0'
            p_pr.append(ind)
            
            # 对齐方式
            jc = document_xml.new_tag('w:jc')
            jc['w:val'] = 'center'
            p_pr.append(jc)
            
            # 运行属性
            r_pr = document_xml.new_tag('w:rPr')
            
            # 字体
            r_fonts = document_xml.new_tag('w:rFonts')
            r_fonts['w:hint'] = 'eastAsia'
            r_fonts['w:ascii'] = '仿宋'
            r_fonts['w:hAnsi'] = '仿宋'
            r_fonts['w:eastAsia'] = '仿宋'
            r_fonts['w:cs'] = '仿宋'
            r_pr.append(r_fonts)
            
            # 加粗
            b = document_xml.new_tag('w:b')
            r_pr.append(b)
            
            p_pr.append(r_pr)
            p.append(p_pr)
            
            # 如果有内容，添加运行和文本
            if cell_data:
                r = document_xml.new_tag('w:r')
                
                # 运行属性
                r_pr_content = document_xml.new_tag('w:rPr')
                r_fonts_content = document_xml.new_tag('w:rFonts')
                r_fonts_content['w:hint'] = 'eastAsia'
                r_fonts_content['w:ascii'] = '仿宋'
                r_fonts_content['w:hAnsi'] = '仿宋'
                r_fonts_content['w:eastAsia'] = '仿宋'
                r_fonts_content['w:cs'] = '仿宋'
                r_pr_content.append(r_fonts_content)
                
                b_content = document_xml.new_tag('w:b')
                r_pr_content.append(b_content)
                r.append(r_pr_content)
                
                # 文本内容
                t = document_xml.new_tag('w:t')
                t.string = str(cell_data)
                r.append(t)
                p.append(r)
            
            tc.append(p)
            tr.append(tc)
        
        return tr

    def _insert_todo_table_rows(self, document_xml: BeautifulSoup, todo_table: Dict):
        """
        在表格中插入待办事项行
        
        Args:
            document_xml: BeautifulSoup文档对象
            todo_table: 待办事项表格数据
        """
        if not todo_table or not todo_table.get('rows'):
            logger.info("没有待办事项数据需要插入")
            return
            
        # 查找目标表格行
        target_tr = None
        for tr in document_xml.find_all('w:tr'):
            if tr.get('w14:paraId') == '6D0D5251':
                target_tr = tr
                break
        
        if not target_tr:
            logger.warning("未找到目标表格行 w14:paraId='6D0D5251'")
            return
        
        logger.info(f"找到目标表格行，准备插入 {len(todo_table['rows'])} 行待办事项")
        
        # 在目标行后面插入新行
        current_element = target_tr
        for row_data in todo_table['rows']:
            if row_data:  # 确保行数据不为空
                # 补齐列数据到3列
                padded_row = list(row_data)
                while len(padded_row) < 3:
                    padded_row.append("")
                
                # 只取前3列
                padded_row = padded_row[:3]
                
                new_row = self._create_table_row(padded_row, document_xml)
                current_element.insert_after(new_row)
                current_element = new_row
                
        logger.info("待办事项表格行插入完成")
    
