import asyncio
import json
import threading
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from openai import OpenAI
import requests
import logging

from app.models import Meeting, beijing_now
from app.mysql import get_db
from app.tasks.executor import upload_result_to_oss
from config import cfg


logger = logging.getLogger(__name__)


class TaskManager(ABC):
    """通用任务管理器基类"""
    
    def __init__(self, max_workers: int = 5, check_interval: int = 5):
        self.is_running = False
        self.worker_thread = None
        self.lock = threading.Lock()
        self.running_tasks: Dict[int, threading.Thread] = {}  # task_id -> thread
        self.task_status: Dict[int, dict] = {}  # task_id -> status info
        self.cancel_flags: Dict[int, threading.Event] = {}  # task_id -> cancel event
        self.max_workers = max_workers  # 最大并发任务数
        self.check_interval = check_interval  # 工作循环检查间隔（秒）
    
    def start(self):
        """启动任务管理器"""
        with self.lock:
            if self.is_running:
                return
            
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
            logger.info(f"{self.__class__.__name__} 任务管理器已启动")
            

    def stop(self):
        """停止任务管理器"""
        with self.lock:
            if not self.is_running:
                return
            
            self.is_running = False
            
            # 设置所有运行中任务的取消标志
            for task_id in list(self.running_tasks.keys()):
                if task_id in self.cancel_flags:
                    self.cancel_flags[task_id].set()
            
            # 等待所有运行中的任务完成
            for task_id, thread in list(self.running_tasks.items()):
                if thread.is_alive():
                    thread.join(timeout=5)  # 最多等待5秒
            
            # 清理所有剩余资源
            self.running_tasks.clear()
            self.cancel_flags.clear()
            
            logger.info(f"{self.__class__.__name__} 任务管理器已停止")

    def submit_task(self, task_id: int) -> bool:
        """提交任务"""
        with self.lock:
            if task_id in self.running_tasks:
                logger.warning(f"任务 {task_id} 已在运行中")
                return False
            
            if len(self.running_tasks) >= self.max_workers:
                logger.warning(f"已达到最大并发任务数 {self.max_workers}，任务 {task_id} 将排队等待")
                return False
            
            try:
                # 更新任务状态到数据库
                if not self._update_task_status_in_db(task_id, self._get_running_status()):
                    return False
                
                # 创建任务线程
                task_thread = threading.Thread(
                    target=self._execute_task_wrapper,
                    args=(task_id,),
                    daemon=True
                )
                
                # 创建取消标志
                self.cancel_flags[task_id] = threading.Event()
                
                self.running_tasks[task_id] = task_thread
                self.task_status[task_id] = {
                    "status": "starting",
                    "progress": 0,
                    "started_at": beijing_now().isoformat(),
                    "error": None
                }
                
                task_thread.start()
                logger.info(f"已提交任务 {task_id}")
                return True
                
            except Exception as e:
                logger.error(f"提交任务失败: {e}")
                return False

    def get_task_status(self, task_id: int) -> Optional[dict]:
        """获取任务状态"""
        with self.lock:
            return self.task_status.get(task_id)

    def cancel_task(self, task_id: int) -> bool:
        """取消任务"""
        with self.lock:
            if task_id not in self.running_tasks:
                logger.warning(f"任务 {task_id} 未在运行中")
                return False
            
            # 设置取消标志
            if task_id in self.cancel_flags:
                self.cancel_flags[task_id].set()
                logger.info(f"已设置任务 {task_id} 的取消标志")
            
            # 更新状态为取消中
            if task_id in self.task_status:
                self.task_status[task_id]["status"] = "canceling"
                self.task_status[task_id]["canceled_at"] = beijing_now().isoformat()
            
            # 获取任务线程
            task_thread = self.running_tasks.get(task_id)
            if task_thread and task_thread.is_alive():
                logger.info(f"正在等待任务 {task_id} 线程停止...")
                
                # 等待线程自然结束，最多等待10秒
                task_thread.join(timeout=10)
                
                if task_thread.is_alive():
                    logger.warning(f"任务 {task_id} 线程在10秒内未能停止")
                    # 注意：Python中无法强制杀死线程，只能等待其自然结束
                    # 线程需要在执行过程中检查cancel_flag来实现优雅退出
                else:
                    logger.info(f"任务 {task_id} 线程已成功停止")
                
                # 清理任务
                self._cleanup_task(task_id)
            
            # 更新数据库状态
            self._update_task_status_in_db(task_id, self._get_canceled_status())
            
            # 最终更新状态为已取消
            if task_id in self.task_status:
                self.task_status[task_id]["status"] = "canceled"
            
            return True

    def _cleanup_task(self, task_id: int):
        """清理任务相关资源"""
        with self.lock:
            # 从运行任务中移除
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            # 清理取消标志
            if task_id in self.cancel_flags:
                del self.cancel_flags[task_id]

    def _is_task_canceled(self, task_id: int) -> bool:
        """检查任务是否被取消"""
        if task_id in self.cancel_flags:
            return self.cancel_flags[task_id].is_set()
        return False

    def _worker_loop(self):
        """工作线程主循环"""
        while self.is_running:
            try:
                # 清理已完成的任务
                self._cleanup_finished_tasks()
                
                # 检查是否有排队的任务需要启动
                self._start_pending_tasks()
                
                time.sleep(self.check_interval)  # 使用配置的检查间隔
                
            except Exception as e:
                logger.error(f"任务管理器工作循环出错: {e}")
                time.sleep(10)

    def _cleanup_finished_tasks(self):
        """清理已完成的任务"""
        with self.lock:
            finished_tasks = []
            for task_id, thread in self.running_tasks.items():
                if not thread.is_alive():
                    finished_tasks.append(task_id)
            
            for task_id in finished_tasks:
                # 从运行任务中移除
                del self.running_tasks[task_id]
                
                # 清理取消标志
                if task_id in self.cancel_flags:
                    del self.cancel_flags[task_id]
                
                # 保留状态信息一段时间，供查询使用
                if task_id in self.task_status:
                    status = self.task_status[task_id]
                    if status.get("status") not in ["completed", "failed", "canceled"]:
                        status["status"] = "completed"

    def _start_pending_tasks(self):
        """启动排队中的任务"""
        if len(self.running_tasks) >= self.max_workers:
            return
            
        try:
            pending_task_ids = self._get_pending_tasks(self.max_workers - len(self.running_tasks))
            
            for task_id in pending_task_ids:
                if task_id not in self.running_tasks:
                    self.submit_task(task_id)
            
        except Exception as e:
            logger.error(f"启动排队任务失败: {e}")

    def _execute_task_wrapper(self, task_id: int):
        """任务执行包装器"""
        try:
            # 更新任务状态
            if task_id in self.task_status:
                self.task_status[task_id]["status"] = "running"
            
            # 执行具体任务（传入取消检查函数）
            self._execute_task(task_id)
            
            # 检查是否被取消
            if self._is_task_canceled(task_id):
                if task_id in self.task_status:
                    self.task_status[task_id].update({
                        "status": "canceled",
                        "canceled_at": beijing_now().isoformat()
                    })
                self._update_task_status_in_db(task_id, self._get_canceled_status())
            else:
                # 更新为完成状态
                if task_id in self.task_status:
                    self.task_status[task_id].update({
                        "status": "completed",
                        "progress": 100,
                        "completed_at": beijing_now().isoformat()
                    })
            
        except Exception as e:
            # 检查是否是因为取消导致的异常
            if self._is_task_canceled(task_id):
                logger.info(f"任务 {task_id} 已被取消")
                if task_id in self.task_status:
                    self.task_status[task_id].update({
                        "status": "canceled",
                        "canceled_at": beijing_now().isoformat()
                    })
                self._update_task_status_in_db(task_id, self._get_canceled_status())
            else:
                logger.error(f"执行任务 {task_id} 失败: {e}")
                # 更新任务状态为失败
                if task_id in self.task_status:
                    self.task_status[task_id].update({
                        "status": "failed",
                        "error": str(e),
                        "failed_at": beijing_now().isoformat()
                    })
                # 更新数据库状态
                self._update_task_status_in_db(task_id, self._get_failed_status())
            
        finally:
            # 清理任务资源
            self._cleanup_task(task_id)

    # 抽象方法，子类必须实现
    @abstractmethod
    def _execute_task(self, task_id: int):
        """执行具体任务 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _get_pending_tasks(self, limit: int) -> List[int]:
        """获取待处理的任务ID列表 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _update_task_status_in_db(self, task_id: int, status: str) -> bool:
        """更新数据库中的任务状态 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _get_running_status(self) -> str:
        """获取运行中状态值 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _get_canceled_status(self) -> str:
        """获取取消状态值 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _get_failed_status(self) -> str:
        """获取失败状态值 - 子类需要实现"""
        pass




class MysqlTaskManager(TaskManager):
    """Mysql任务管理器"""
    
    def __init__(self, model_class, check_interval: int = 5):
        super().__init__(max_workers=5, check_interval=check_interval)
        self.model_class = model_class  # 数据库模型类
     
        

    def _execute_task(self, task_id: int):
        """执行AI优化任务"""
        db = None
        try:
            logger.info(f"开始执行任务 {task_id}")
            
            # 检查是否被取消
            if self._is_task_canceled(task_id):
                logger.info(f"任务 {task_id} 在开始前已被取消")
                return
            
            db = next(get_db())
            meeting = db.query(self.model_class).filter(self.model_class.id == task_id).first()
            
            if not meeting:
                raise Exception(f"任务 {task_id} 不存在")
            
            # 再次检查是否被取消
            if self._is_task_canceled(task_id):
                logger.info(f"任务 {task_id} 在执行前已被取消")
                return
            
            # 执行具体任务，传入取消检查函数
            self._execute_task_item(meeting, db, lambda: self._is_task_canceled(task_id))
            
        finally:
            if db:
                db.close()

    @abstractmethod
    def _execute_task_item(self, taskModel, db, is_canceled_func):
        """执行具体任务"""
        pass
    
    def _get_pending_tasks(self, limit: int) -> List[int]:
        """获取待处理的任务ID列表"""
        try:
            db = next(get_db())
            pendings = db.query(self.model_class).filter(
                self.model_class.status == "created"
            ).limit(limit).all()
            result = [item.id for item in pendings]
            db.close()
            return result
        except Exception as e:
            logger.error(f"获取待处理任务失败: {e}")
            return []

    def _update_task_status_in_db(self, task_id: int, status: str) -> bool:
        """更新数据库中的任务状态"""
        try:
            db = next(get_db())
            meeting = db.query(self.model_class).filter(self.model_class.id == task_id).first()
            if not meeting:
                logger.error(f"任务 {task_id} 不存在")
                return False
            
            meeting.status = status
            meeting.updatedAt = beijing_now()
            db.commit()
            db.close()
            return True
            
        except Exception as e:
            logger.error(f"更新数据库状态失败: {e}")
            return False

    def _get_running_status(self) -> str:
        """获取运行中状态值"""
        return "started"
    
    def _get_canceled_status(self) -> str:
        """获取取消状态值"""
        return "canceled"  # 回到文本化完成状态
    
    def _get_failed_status(self) -> str:
        """获取失败状态值"""
        return "failed"
