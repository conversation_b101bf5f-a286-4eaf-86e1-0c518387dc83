import json
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, or_
from typing import List, Literal, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime

from app.models import Meeting, MeetingTextRefineTask,MeetingSummeryTask,MeetingRefineTask, HttpResult, success, error, TaskStatus, beijing_now
import logging
from app.mysql import get_db
from app.utils import copy_file_to_oss, upload_text_to_oss
from config import cfg
from app.models import Meeting
from app.tasks.utils import startRefineTask, startSummeryTask

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/api/task", tags=["任务回调"])

@router.post("/callback", response_model=HttpResult[bool], summary="耗时任务回调")
async def task_callback(
    taskPayload: dict,
    db: Session = Depends(get_db)
):
    """
    耗时任务回调函数
    """
    print("task_callback->", taskPayload)

    appId =  taskPayload.get("app_id", "")
    if appId != cfg.CELERY.TASK_APP_ID:
        raise Exception("task 回调地址错误")
    
    status = taskPayload.get("status", "")
    inputs = taskPayload.get("inputs", {})
    result = taskPayload.get("result", {})
    queueName = taskPayload.get("queue_name", "")
    taskId = taskPayload.get("task_id", "")
    
    logger.info(f"任务回调 - 状态SUCCESS: {status == TaskStatus.SUCCESS.value}, 队列: {queueName}, 任务ID: {taskId}")
    logger.info(f"任务输入: {inputs}")
    logger.info(f"任务结果: {result}")


    if queueName == cfg.CELERY.TASK_ASR_QUEUE:
        handle_asr_task_callback(taskId, status, inputs, result, db) 
    elif queueName == cfg.CELERY.TASK_REFINE_QUEUE:
        handle_text_refine_callback(taskId, status, inputs, result, db)
    elif queueName == cfg.CELERY.TASK_SUMMERY_QUEUE:
        handle_text_summery_callback(taskId, status, inputs, result, db)

    return success(True)

def handle_asr_task_callback(taskId, status, inputs, result, db):
    # 查找对应的会议记录
    meeting = db.query(Meeting).filter(Meeting.asrTaskId == taskId).first()
    if not meeting:
        logger.warning(f"未找到任务ID为 {taskId} 的会议记录")
        return
    
    # 记录原状态，用于日志
    old_state = meeting.audioState

    if status == TaskStatus.SUCCESS.value:
        meeting.audioState = "finished"
        result = result["result"]
        print(result)
        url = result.get('audio_file_url', '')
        if url:
            logger.info(f"音频文件已生成，URL: {url}")
            meeting.audioUrl = url
            duration = result.get('audio_duration_s', 0)
            if duration > 0:
                meeting.audioDuration = duration
            fileisze = int(result.get('file_size_mb', 0) * 1024 * 1024)
            if fileisze > 0:
                meeting.audioSize = fileisze

        meeting.textUrl = result.get('result_url', '')

        print("="*20)
        print(meeting.audioUrl, meeting.textUrl)

    else:
        meeting.audioState = "failed"
        meeting.updatedAt = beijing_now()
    
    db.commit()

    # 记录状态变化
    if old_state != meeting.audioState :
        logger.info(f"会议 {meeting.id} 状态更新: {old_state} -> {meeting.audioState}")
    
    # 提交AI模型优化
    if status == TaskStatus.SUCCESS.value and len(meeting.textUrl) > 0:     
        #添加微调任务
        taskResult = startSummeryTask(meeting.textUrl, "", f"asr_summery")
        if taskResult["task_id"]:
            meeting.summeryState = "started"
            meeting.summeryTaskId = taskResult["task_id"]
            db.commit()


def handle_text_refine_callback(taskId, status, inputs, result, db):
    # 查找对应的会议记录
    refineTask = db.query(MeetingRefineTask).filter(MeetingRefineTask.textTaskId == taskId).first()
    if not refineTask:
        logger.warning(f"未找到任务ID为 {taskId} 的优化记录")
        return
    
    # 记录原状态，用于日志
    old_state = refineTask.textRefineState
        
    if status == TaskStatus.SUCCESS.value:
        refineTask.textRefineState = "success"            
        resultUrl = result.get("resultUrl", "")
        refineTask.textRefinedUrl = resultUrl
        print("="*20)
        print(refineTask.textRefinedUrl)

    else:
        refineTask.textRefineState = "failed"
    
    refineTask.updatedAt = beijing_now()
    db.commit()

    # 记录状态变化
    if old_state != refineTask.textRefineState:
        logger.info(f"优化任务 {refineTask.id} 状态更新: {old_state} -> {refineTask.textRefineState}")
    
    # 提交AI模型优化
    if status == TaskStatus.SUCCESS.value and len(refineTask.textRefinedUrl) > 0:
        print("走会议纪要流程")
        
        #添加微调任务
        taskResult = startSummeryTask(refineTask.textRefinedUrl, refineTask.refTextUrl, "data_summery")
        if taskResult["task_id"]:
            refineTask.summeryTaskId = taskResult["task_id"]
            refineTask.summeryRefineState = "started"
        else:
            refineTask.summeryRefineState = "failed"

        refineTask.updatedAt = beijing_now()
        db.commit()
        
def handle_text_summery_callback(taskId, status, inputs, result, db):

    data_id = result.get("data_id", "")
    if data_id == "asr_summery":
        # 查找对应的会议记录
        meeting = db.query(Meeting).filter(Meeting.summeryTaskId == taskId).first()
        if not meeting:
            logger.warning(f"未找到任务ID为 {taskId} 的会议纪要任务")
            return
    

        # 记录原状态，用于日志
        old_state = meeting.summeryState
            
        if status == TaskStatus.SUCCESS.value:
            meeting.summeryState = "success"            
            resultUrl = result.get("resultUrl", "")
            meeting.summaryUrl = resultUrl
            print("="*20)
            print(meeting.summaryUrl)

        else:
            meeting.summeryState = "failed"
            meeting.updatedAt = beijing_now()

        db.commit()

        # 记录状态变化
        if old_state != meeting.summeryState:
            logger.info(f"会议纪要任务 {meeting.id} 状态更新: {old_state} -> {meeting.summeryState}")

        return
    
    if data_id == "data_summery":
        # 查找对应的会议记录
        task = db.query(MeetingRefineTask).filter(MeetingRefineTask.summeryTaskId == taskId).first()
        if not task:
            logger.warning(f"未找到任务ID为 {taskId} 的会议纪要任务")
            return
             # 记录原状态，用于日志
        old_state = task.summeryRefineState

        if status == TaskStatus.SUCCESS.value:
            task.summeryRefineState = "success"
            task.summeryRefinedUrl = result.get("resultUrl", "")
            print("="*20)
            print(task.summeryRefinedUrl)
        else:
            task.summeryRefineState = "failed"

        task.updatedAt = beijing_now()
        db.commit()

        # 记录状态变化
        if old_state != task.summeryRefineState:
            logger.info(f"会议纪要任务 {task.id} 状态更新: {old_state} -> {task.summeryRefineState}")
            
        return