import json
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, or_
from typing import List, Literal, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime

from app.models import Meeting, MeetingTextRefineTask,MeetingSummeryTask, HttpResult, success, error, TaskStatus
import logging
from app.mysql import get_db
from app.utils import copy_file_to_oss, upload_text_to_oss
from config import cfg
from app.models import Meeting
from app.tasks.utils import startRefineTask, startSummeryTask

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/api/task", tags=["任务回调"])

@router.post("/callback", response_model=HttpResult[bool], summary="耗时任务回调")
async def task_callback(
    taskPayload: dict,
    db: Session = Depends(get_db)
):
    """
    耗时任务回调函数
    """
    print("task_callback->", taskPayload)

    appId =  taskPayload.get("app_id", "")
    if appId != cfg.CELERY.TASK_APP_ID:
        raise Exception("task 回调地址错误")
    
    status = taskPayload.get("status", "")
    inputs = taskPayload.get("inputs", {})
    result = taskPayload.get("result", {})
    queueName = taskPayload.get("queue_name", "")
    taskId = taskPayload.get("task_id", "")
    
    logger.info(f"任务回调 - 状态SUCCESS: {status == TaskStatus.SUCCESS.value}, 队列: {queueName}, 任务ID: {taskId}")
    logger.info(f"任务输入: {inputs}")
    logger.info(f"任务结果: {result}")


    if queueName == cfg.CELERY.TASK_ASR_QUEUE:
        handle_asr_task_callback(taskId, status, inputs, result, db) 
    elif queueName == cfg.CELERY.TASK_REFINE_QUEUE:
        handle_text_refine_callback(taskId, status, inputs, result, db)
    elif queueName == cfg.CELERY.TASK_SUMMERY_QUEUE:
        handle_text_summery_callback(taskId, status, inputs, result, db)

    return success(True)

def handle_asr_task_callback(taskId, status, inputs, result, db):
    # 查找对应的会议记录
    meeting = db.query(Meeting).filter(Meeting.celeryTaskId == taskId).first()
    if not meeting:
        logger.warning(f"未找到任务ID为 {taskId} 的会议记录")
        return
    
    # 记录原状态，用于日志
    old_state = meeting.audioState
    old_task_status = meeting.celeryTaskStatus
        
    if status == TaskStatus.SUCCESS.value:
        meeting.audioState = "finished"
        meeting.celeryTaskStatus = "finished"
        meeting.status = "texted"                
        meeting.celeryTaskResult = json.dumps(result, ensure_ascii=False)

        result = result["result"]
        print(result)
        url = result.get('audio_file_url', '')
        if url:
            logger.info(f"音频文件已生成，URL: {url}")
            meeting.audioUrl = url
            duration = result.get('audio_duration_s', 0)
            if duration > 0:
                meeting.audioDuration = duration
            fileisze = int(result.get('file_size_mb', 0) * 1024 * 1024)
            if fileisze > 0:
                meeting.audioSize = fileisze

        meeting.textUrl = result.get('result_url', '')

        print("="*20)
        print(meeting.audioUrl, meeting.textUrl)

    else:
        meeting.audioState = "failed"
        meeting.celeryTaskStatus = "failed"
        meeting.updatedAt = datetime.utcnow()
    
    db.commit()

    # 记录状态变化
    if old_state != meeting.audioState or old_task_status != meeting.celeryTaskStatus:
        logger.info(f"会议 {meeting.id} 状态更新: {old_state}/{old_task_status} -> {meeting.audioState}/{meeting.celeryTaskStatus}")
    
    # 提交AI模型优化
    if status == TaskStatus.SUCCESS.value and len(meeting.textUrl) > 0:
        ref_text_url = ""
        if meeting.desc and len(meeting.desc) > 0:
            ref_text_url = upload_text_to_oss(ref_text_url, meeting.id, prefix=f"refinetask/{meeting.id}")
        
        text_url = meeting.textUrl

        # 创建新的优化任务
        new_task = MeetingTextRefineTask(
            versionName="AI优化",
            meeting_id=meeting.id,
            textUrl=text_url,
            refTextUrl=ref_text_url,
            status="created"
        )
        db.add(new_task)
        db.commit()
        db.refresh(new_task)
        
        #添加微调任务
        taskResult = startRefineTask(meeting.textUrl, ref_text_url, f"{new_task.id}")
        if taskResult["task_id"]:
            new_task.taskId = taskResult["task_id"]
            db.commit()



def handle_text_refine_callback(taskId, status, inputs, result, db):
    # 查找对应的会议记录
    refineTask = db.query(MeetingTextRefineTask).filter(MeetingTextRefineTask.taskId == taskId).first()
    if not refineTask:
        logger.warning(f"未找到任务ID为 {taskId} 的优化记录")
        return
    
    # 记录原状态，用于日志
    old_state = refineTask.status
        
    if status == TaskStatus.SUCCESS.value:
        refineTask.status = "success"            
        resultUrl = result.get("resultUrl", "")
        refineTask.resultUrl = resultUrl
        print("="*20)
        print(refineTask.resultUrl)

    else:
        refineTask.status = "failed"
        refineTask.updatedAt = datetime.utcnow()
    
    db.commit()

    # 记录状态变化
    if old_state != refineTask.status:
        logger.info(f"优化任务 {refineTask.id} 状态更新: {old_state} -> {refineTask.status}")
    
    # 提交AI模型优化
    if status == TaskStatus.SUCCESS.value and len(refineTask.resultUrl) > 0:
        print("走会议纪要流程")
        
        meeting = db.query(Meeting).filter(Meeting.id == refineTask.meeting_id).first()
        if not meeting or meeting.summaryUrl: #只有第一次微调模型的时候，才走一次会议纪要，后续都是用户手动开启相应的微调任务，不自动生成纪要内容
            return
        
        print("首次创建会议纪要内容")

        # 创建新的优化任务
        new_task = MeetingSummeryTask(
            versionName="默认会议纪要",
            meeting_id=refineTask.meeting_id,
            textUrl=refineTask.resultUrl,
            refTextUrl=refineTask.refTextUrl,
            status="created"
        )
        db.add(new_task)
        db.commit()
        db.refresh(new_task)

        #添加微调任务
        taskResult = startSummeryTask(new_task.textUrl, new_task.refTextUrl, f"{new_task.id}")
        if taskResult["task_id"]:
            new_task.taskId = taskResult["task_id"]
            db.commit()
        
def handle_text_summery_callback(taskId, status, inputs, result, db):
    # 查找对应的会议记录
    task = db.query(MeetingSummeryTask).filter(MeetingSummeryTask.taskId == taskId).first()
    if not task:
        logger.warning(f"未找到任务ID为 {taskId} 的优化记录")
        return
    
    # 记录原状态，用于日志
    old_state = task.status
        
    if status == TaskStatus.SUCCESS.value:
        task.status = "success"            
        resultUrl = result.get("resultUrl", "")
        task.resultUrl = resultUrl
        print("="*20)
        print(task.resultUrl)

    else:
        task.status = "failed"
        task.updatedAt = datetime.utcnow()
    db.commit()

    # 记录状态变化
    if old_state != task.status:
        logger.info(f"优化任务 {task.id} 状态更新: {old_state} -> {task.status}")

    # 首次总结内容
    meeting = db.query(Meeting).filter(Meeting.id == task.meeting_id).first()
    if meeting and not meeting.summaryUrl: #只有第一次微调模型的时候，才走一次会议纪要，后续都是用户手动开启相应的微调任务，不自动生成纪要内容
        meeting.summaryUrl = task.resultUrl
        db.commit()
        print("首次更新会议的纪要链接")
        return
    