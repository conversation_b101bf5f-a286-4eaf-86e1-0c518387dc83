import json
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, or_
from typing import List, Literal, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime

from app.models import Meeting, MeetingRefineTask, HttpResult, success, error, beijing_now
import logging
from app.mysql import get_db
from app.utils import copy_file_to_oss
from app.tasks.utils import startRefineTask, startSummeryTask
from app.utils import get_current_user

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/api/refine2", tags=["会议资料微调"])


# 分页响应模型
class RefineTaskResponse(BaseModel):
    """
    文本优化任务响应模型
    
    包含任务的详细信息，用于列表展示和详情查看
    """
    id: int = Field(description="任务ID", example=1)
    versionName: str = Field(description="版本名称", example="v1")
    meeting_id: int = Field(description="关联的会议ID", example=123)
    textUrl: str = Field(description="原始文本文件URL")
    refTextUrl: Optional[str] = Field(default=None, description="参考文本文件URL")
    
    textRefinedUrl: Optional[str] = Field(default=None, description="文本优化结果文件URL")
    textRefineState: str = Field(description="文本优化状态,waiting(等待),started(进行中),success(成功),failed(失败)")

    summeryRefinedUrl: Optional[str] = Field(default=None, description="纪要优化结果文件URL")
    summeryRefineState: str = Field(description="纪要优化状态,waiting(等待),started(进行中),success(成功),failed(失败)")

    createdAt: datetime = Field(description="创建时间")
    updatedAt: datetime = Field(description="更新时间")


# 创建任务请求模型
class CreateRefineTaskRequest(BaseModel):
    """
    创建文本优化任务的请求模型
    
    用于创建会议文本优化任务，支持提供参考文本来指导优化过程
    """
    meeting_id: int = Field(
        description="会议ID，必须是已存在的会议",
        example=123,
        gt=0
    )
    versionName: Optional[str] = Field(
        default=None,
        description="版本名称，如不提供则自动生成（格式：v1, v2, v3...）",
        example="v1",
        max_length=32
    )

    textUrl: Optional[str] = Field(
        default=None,
        description="带优化的语音文本内容",
        example="https://example.com/reference.txt"
    )


    refTextUrl: Optional[str] = Field(
        default=None,
        description="参考文本URL，用于指导文本优化的参考资料",
        example="https://example.com/reference.txt"
    )

    needRefineText: bool = Field(
        default=False,
        description="是否需要优化文本",
        example=False
    )
    
class PaginatedTaskResponse(BaseModel):
    """
    分页任务列表响应模型
    
    包含任务列表和分页信息
    """
    list: List[RefineTaskResponse] = Field(description="任务列表")
    total: int = Field(description="总记录数", example=100)
    page: int = Field(description="当前页码", example=1)
    page_size: int = Field(description="每页数量", example=10)
    total_pages: int = Field(description="总页数", example=10)

# 创建优化任务
@router.post("/create", response_model=HttpResult[int], summary="创建会议资料优化任务")
async def create_refine_task(
    task_request: CreateRefineTaskRequest,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user),
):
    """
    创建会议资料优化任务
    
    ## 功能说明
    为指定会议创建会议资料优化任务，可以提供参考文本来指导优化过程。
    
    ## 请求参数说明
    - **meeting_id**: 必须是已存在且有文本内容的会议ID
    - **versionName**: 可选，版本名称，如不提供则自动生成（v1, v2, v3...）
    - **refTextUrl**: 可选，参考文本URL，用于指导文本优化
    - **textUrl**: 必选，带优化的语音文本内容
    
    ## 返回结果
    返回创建成功的任务ID

    ## 错误情况
    - 404: 会议不存在
    - 400: 会议文本不存在
    - 500: 服务器内部错误
    
    ## 注意事项
    - 系统会自动将文本文件复制到OSS存储
    - 任务创建后状态为 "created"，需要通过其他接口启动任务
    """
    # 检查会议是否存在
    meeting_exists = db.query(Meeting).filter(Meeting.id == task_request.meeting_id).first()
    if not meeting_exists:
        raise HTTPException(status_code=404, detail="会议不存在")
    
    if task_request.textUrl is None:
        raise HTTPException(status_code=400, detail="会议文本不存在")
    
    if task_request.versionName is None:
        count = db.query(MeetingRefineTask).filter(MeetingRefineTask.meeting_id == task_request.meeting_id).count()
        task_request.versionName = f"v{count+1}" 

    # 拷贝文件到Oss

    ref_text_url = task_request.refTextUrl
    text_url = task_request.textUrl

    # 创建新的优化任务
    new_task = MeetingRefineTask(
        versionName=task_request.versionName,
        meeting_id=task_request.meeting_id,
        textUrl=text_url,
        refTextUrl=ref_text_url,
        textRefineState="waiting",
        summeryRefineState="waiting",
        textRefinedUrl="",
        summeryRefinedUrl="",
        textTaskId="",
        summeryTaskId="",
        user_id=current_user_id
    )
    db.add(new_task)
    if not task_request.needRefineText:
        new_task.textRefineState = "success"
        new_task.textRefinedUrl = text_url

    db.commit()
    db.refresh(new_task)

    if task_request.needRefineText:
        # 提交文字优化任务
        result = startRefineTask(text_url, ref_text_url, new_task.id)
        if result.get("task_id"):
            new_task.textRefineState = "started"
            new_task.textTaskId = result.get("task_id")
        else:
            new_task.textRefineState = "failed"
    else:

        #直接提交总结任务
        result = startSummeryTask(text_url, ref_text_url, "data_summery")
        if result.get("task_id"):
            new_task.summeryRefineState = "started"
            new_task.summeryTaskId = result.get("task_id")
        else:
            new_task.summeryRefineState = "failed"
    
    db.commit()

    # 拷贝文件到Oss
    return success(new_task.id)
 

# 删除优化任务
@router.delete("/delete/{task_id}", response_model=HttpResult[str], summary="删除优化任务")
async def delete_refine_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user),
):
    """
    删除文本优化任务
    """
    try:
        # 查找任务
        task = db.query(MeetingRefineTask).filter(MeetingRefineTask.id == task_id, MeetingRefineTask.user_id == current_user_id).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
      
        # 删除任务
        db.delete(task)
        db.commit()
        
        return success("任务删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除优化任务失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail="删除优化任务失败")

# 获取优化结果
@router.get("/list", response_model=HttpResult[PaginatedTaskResponse], summary="获取优化任务列表")
async def get_meetings(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
    meeting_id: int = Query(-1, ge=-1, description="会议ID"),
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user),
):
    """
    获取优化任务列表
    """

    try:
        # 计算偏移量
        offset = (page - 1) * page_size

        base_filter = MeetingRefineTask.user_id == current_user_id
        if meeting_id != -1:
            base_filter = MeetingRefineTask.meeting_id == meeting_id

       
        # 查询总数
        total_count_query = db.query(func.count(MeetingRefineTask.id))\
            .filter(base_filter)
        
        # 打印SQL语句用于调试
        # print("SQL查询语句:", str(total_count_query.statement.compile(compile_kwargs={"literal_binds": True})))
        
        total_count = total_count_query.scalar()

        # 查询会议列表（分页）
        tasks_query = db.query(
            MeetingRefineTask.id,
            MeetingRefineTask.versionName,
            MeetingRefineTask.meeting_id,
            MeetingRefineTask.textUrl,
            MeetingRefineTask.refTextUrl,            
            MeetingRefineTask.textRefinedUrl,
            MeetingRefineTask.summeryRefinedUrl,
            MeetingRefineTask.textRefineState,
            MeetingRefineTask.summeryRefineState,
            MeetingRefineTask.createdAt,
            MeetingRefineTask.updatedAt
        ).filter(base_filter)\
         .order_by(MeetingRefineTask.createdAt.desc())\
         .offset(offset)\
         .limit(page_size)
        
        # 打印分页查询的SQL语句
        # print("分页查询SQL:", str(tasks_query.statement.compile(compile_kwargs={"literal_binds": True})))
        
        tasks = tasks_query.all()

        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size

        # 格式化响应数据
        list = []
        for task in tasks:
            list.append(RefineTaskResponse(
                id=task.id,
                versionName= task.versionName,
                meeting_id=task.meeting_id,
                textUrl=task.textUrl,
                refTextUrl=task.refTextUrl,
                textRefinedUrl=task.textRefinedUrl,
                summeryRefinedUrl=task.summeryRefinedUrl,
                textRefineState=task.textRefineState,
                summeryRefineState=task.summeryRefineState,
                createdAt=task.createdAt,
                updatedAt=task.updatedAt
            ))

        return success(PaginatedTaskResponse(
            list=list,
            total=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))

    except Exception as e:
        logger.error(f"获取优化列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取优化表失败")

# 更新会议请求模型
class UpdateRefinedTextRequest(BaseModel):
    id: int
    textRefinedUrl: str
    summeryRefinedUrl: str


@router.post("/update", response_model=HttpResult[bool], summary="更新优化后的文本")
async def update_refined_text(
    req: UpdateRefinedTextRequest,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user),
):
    """
    更新优化后的文本
    """
    # 验证会议存在且属于当前用户
    task = db.query(MeetingRefineTask).filter(
        MeetingRefineTask.id == req.id,
        MeetingRefineTask.user_id == current_user_id
    ).first()

    if not task:
        raise HTTPException(status_code=404, detail="优化文本不存在或无权访问")
    
    task.textRefinedUrl = req.textRefinedUrl
    task.summeryRefinedUrl = req.summeryRefinedUrl
    # 更新修改时间
    task.updatedAt = beijing_now()

    db.commit()

    return success(True)