import time
import json
import base64
import hashlib
import hmac
from datetime import datetime, timedelta
from typing import Optional

import oss2
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from config import cfg
from app.models import Meeting, HttpResult, success, error, beijing_now

router = APIRouter(prefix="/v1/api/oss", tags=["OSS"])


class UploadTokenRequest(BaseModel):
    """上传Token请求模型"""
    file_name: Optional[str] = None
    content_type: Optional[str] = None
    dir_prefix: str = "uploads/"  # 文件上传目录前缀


class UploadTokenResponse(BaseModel):
    """上传Token响应模型"""
    access_key_id: str
    policy: str
    signature: str
    dir: str
    host: str
    expire: int
    callback: Optional[str] = None


def generate_policy(bucket_name: str, dir_prefix: str, max_size: int, expire_time: int) -> dict:
    """生成上传策略"""
    now = beijing_now()
    expire_date = now + timedelta(seconds=expire_time)
    expire_syncpoint = int(expire_date.timestamp())
    
    policy_dict = {
        "expiration": expire_date.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
        "conditions": [
            {"bucket": bucket_name},
            ["starts-with", "$key", dir_prefix],
            {"x-oss-object-acl": "public-read"},
            ["content-length-range", 0, max_size]
        ]
    }
    
    return policy_dict


def generate_signature(policy_str: str, access_key_secret: str) -> str:
    """生成HMAC-SHA1签名"""
    policy_encode = base64.b64encode(policy_str.encode()).decode()
    h = hmac.new(access_key_secret.encode(), policy_encode.encode(), hashlib.sha1)
    signature = base64.b64encode(h.digest()).decode()
    return signature


@router.post("/upload-token", response_model=UploadTokenResponse, summary="获取OSS上传凭证")
async def get_upload_token(request: UploadTokenRequest):
    """
    获取阿里云OSS临时上传凭证
    
    前端获取到凭证后，可直接向OSS发起POST请求上传文件
    """
    try:
        # 从配置中获取OSS参数
        access_key_id = cfg.OSS.ACCESS_KEY_ID
        access_key_secret = cfg.OSS.ACCESS_KEY_SECRET
        bucket_name = cfg.OSS.BUCKET_NAME
        endpoint = cfg.OSS.ENDPOINT
        expire_time = cfg.OSS.EXPIRE_TIME
        max_size = cfg.OSS.MAX_SIZE
        
        # 验证配置是否完整
        if not all([access_key_id, access_key_secret, bucket_name, endpoint]):
            raise HTTPException(status_code=500, detail="OSS配置不完整，请检查配置文件")
        
        # 生成上传目录（如果有文件名，可以生成基于时间的目录结构）
        now = beijing_now()
        dir_prefix = f"{request.dir_prefix}{now.year}/{now.month:02d}/{now.day:02d}/"
        
        # 生成policy
        policy_dict = generate_policy(bucket_name, dir_prefix, max_size, expire_time)
        policy_str = json.dumps(policy_dict, separators=(',', ':'))
        policy_encode = base64.b64encode(policy_str.encode()).decode()
        
        # 生成签名
        signature = generate_signature(policy_str, access_key_secret)
        
        # 构建host
        host = f"https://{bucket_name}.{endpoint}"
        
        # 返回上传凭证
        return UploadTokenResponse(
            access_key_id=access_key_id,
            policy=policy_encode,
            signature=signature,
            dir=dir_prefix,
            host=host,
            expire=int(time.time()) + expire_time
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成上传凭证失败: {str(e)}")


@router.get("/upload-token", response_model=UploadTokenResponse, summary="获取OSS上传凭证(GET)")
async def get_upload_token_get(
    file_name: Optional[str] = Query(None, description="文件名"),
    content_type: Optional[str] = Query(None, description="文件类型"),
    dir_prefix: str = Query("uploads/", description="上传目录前缀")
):
    """
    获取阿里云OSS临时上传凭证 (GET方式)
    """
    request = UploadTokenRequest(
        file_name=file_name,
        content_type=content_type,
        dir_prefix=dir_prefix
    )
    return await get_upload_token(request)