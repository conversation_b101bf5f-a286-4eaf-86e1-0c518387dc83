from fastapi import APIRouter, HTTPException, UploadFile, File
from io import BytesIO
from app.models import HttpResult, success
import logging
import csv
import json
import re

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/api/readtext", tags=["提取文件文本"])


@router.post("/file", response_model=HttpResult[str], summary="上传文件")
async def upload_file(
    file: UploadFile = File(...)
):
    """
    上传文件, 返回文件内容文本
    支持的文件格式: pdf, docx, doc, txt, rtf, html, csv, json
    """
    file_content = await file.read()

    #根据文件名称后缀        
    file_type = file.filename.split(".")[-1].lower()                   
    if file_type == "pdf":
        #提取pdf内容
        file_content_str = extract_pdf_content(file_content)
    elif file_type == "docx":
        #提取docx内容
        file_content_str = extract_docx_content(file_content)
    elif file_type == "doc":
        #提取doc内容
        file_content_str = extract_doc_content(file_content)
    elif file_type == "txt":
        #提取txt内容
        file_content_str = extract_txt_content(file_content)
    elif file_type == "rtf":
        #提取rtf内容
        file_content_str = extract_rtf_content(file_content)
    elif file_type == "html":
        #提取html内容
        file_content_str = extract_html_content(file_content)
    elif file_type == "csv":
        #提取csv内容
        file_content_str = extract_csv_content(file_content)
    elif file_type == "json":
        #提取json内容
        file_content_str = extract_json_content(file_content)
    else:
        raise HTTPException(status_code=400, detail=f"文件类型 {file_type} 不支持，支持的类型: pdf, docx, doc, txt, rtf, html, csv, json")
    
    # 将文件内容文本返回
    return success(file_content_str)


def extract_pdf_content(file_content):
    """提取PDF文件内容"""
    try:
        from PyPDF2 import PdfReader
        
        # 创建PdfReader对象
        reader = PdfReader(BytesIO(file_content))    
        # 遍历PDF中的每一页
        text = ""
        for page in reader.pages:
            # 提取当前页的文本
            text += page.extract_text()
        return text
    except Exception as e:
        logger.error(f"PDF提取失败: {str(e)}")
        raise HTTPException(status_code=400, detail="PDF文件解析失败")

def extract_docx_content(file_content):
    """提取DOCX文件内容"""
    try:
        # 使用python-docx库提取DOCX内容
        from docx import Document
        
        # 创建Document对象，使用BytesIO将字节数据转换为文件对象
        doc = Document(BytesIO(file_content))
        # 遍历文档中的每个段落
        text = ""
        for paragraph in doc.paragraphs:
            # 提取当前段落的文本
            text += paragraph.text + "\n"
        return text.strip()
    except Exception as e:
        logger.error(f"DOCX提取失败: {str(e)}")
        raise HTTPException(status_code=400, detail="DOCX文件解析失败")

def extract_doc_content(file_content):
    """提取DOC文件内容"""
    try:
        # 对于DOC文件，尝试转换为DOCX后处理
        # 这里可以添加转换逻辑，暂时返回提示
        return "DOC文件格式暂不支持，请转换为DOCX格式"
    except Exception as e:
        logger.error(f"DOC提取失败: {str(e)}")
        raise HTTPException(status_code=400, detail="DOC文件解析失败")

def extract_txt_content(file_content):
    """提取TXT文件内容"""
    try:
        # 尝试不同的编码格式
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
        for encoding in encodings:
            try:
                return file_content.decode(encoding)
            except UnicodeDecodeError:
                continue
        raise HTTPException(status_code=400, detail="无法识别TXT文件编码")
    except Exception as e:
        logger.error(f"TXT提取失败: {str(e)}")
        raise HTTPException(status_code=400, detail="TXT文件解析失败")

def extract_rtf_content(file_content):
    """提取RTF文件内容"""
    try:
        # 使用striprtf库提取RTF内容
        from striprtf.striprtf import rtf_to_text
        
        rtf_text = file_content.decode('utf-8')
        plain_text = rtf_to_text(rtf_text)
        return plain_text
    except Exception as e:
        logger.error(f"RTF提取失败: {str(e)}")
        raise HTTPException(status_code=400, detail="RTF文件解析失败")

def extract_html_content(file_content):
    """提取HTML文件内容"""
    try:
        from bs4 import BeautifulSoup
        
        html_text = file_content.decode('utf-8')
        soup = BeautifulSoup(html_text, 'html.parser')
        
        # 移除script和style标签
        for script in soup(["script", "style"]):
            script.decompose()
        
        # 获取文本内容
        text = soup.get_text()
        
        # 清理多余的空白字符
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text
    except Exception as e:
        logger.error(f"HTML提取失败: {str(e)}")
        raise HTTPException(status_code=400, detail="HTML文件解析失败")

def extract_csv_content(file_content):
    """提取CSV文件内容"""
    try:
        csv_text = file_content.decode('utf-8')
        csv_reader = csv.reader(csv_text.splitlines())
        
        text_lines = []
        for row in csv_reader:
            if row:  # 跳过空行
                text_lines.append(' | '.join(row))
        
        return '\n'.join(text_lines)
    except Exception as e:
        logger.error(f"CSV提取失败: {str(e)}")
        raise HTTPException(status_code=400, detail="CSV文件解析失败")

def extract_json_content(file_content):
    """提取JSON文件内容"""
    try:
        json_text = file_content.decode('utf-8')
        json_data = json.loads(json_text)
        
        # 将JSON转换为可读的文本格式
        def json_to_text(obj, indent=0):
            if isinstance(obj, dict):
                text_lines = []
                for key, value in obj.items():
                    text_lines.append("  " * indent + f"{key}: {json_to_text(value, indent + 1)}")
                return "\n".join(text_lines)
            elif isinstance(obj, list):
                text_lines = []
                for i, item in enumerate(obj):
                    text_lines.append("  " * indent + f"[{i}]: {json_to_text(item, indent + 1)}")
                return "\n".join(text_lines)
            else:
                return str(obj)
        
        return json_to_text(json_data)
    except Exception as e:
        logger.error(f"JSON提取失败: {str(e)}")
        raise HTTPException(status_code=400, detail="JSON文件解析失败")