from app.tasks import celery_app
import os
import json
import oss2
from datetime import datetime
from config import cfg

from app.models import beijing_now

def upload_result_to_oss(result_data: dict, meeting_id: int) -> str:
    """
    将转写结果上传到OSS
    
    Args:
        result_data: 转写结果数据
        meeting_id: 会议ID
        
    Returns:
        str: OSS文件URL
    """
    try:
        # 配置OSS
        auth = oss2.Auth(cfg.OSS.ACCESS_KEY_ID, cfg.OSS.ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, cfg.OSS.ENDPOINT, cfg.OSS.BUCKET_NAME)
        
        # 生成文件名和路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"transcription_results/{datetime.now().year}/{datetime.now().month:02d}/{meeting_id}_{timestamp}.json"
        
        # 将结果转换为JSON字符串
        json_content = json.dumps(result_data, ensure_ascii=False, indent=2)
        
        # 上传到OSS
        bucket.put_object(filename, json_content, headers={'Content-Type': 'application/json'})
        
        # 生成文件URL
        file_url = f"https://{cfg.OSS.BUCKET_NAME}.{cfg.OSS.ENDPOINT}/{filename}"
        
        print(f"✅ 转写结果已上传到OSS: {file_url}")
        return file_url
        
    except Exception as e:
        print(f"❌ 上传转写结果到OSS失败: {e}")
        raise

@celery_app.task(bind=True, name='transcribe_audio')
def transcribe_audio_task(self, id: int, audio_origin_urls: list[str]):
    """
    Celery 任务：音频转写
    
    Args:
        id: 任务ID
        audio_origin_urls: 音频文件路径列表
    """
    try:
        from app.tasks.funasr import process_single_file, download_audio_files_and_merge

        # 下载对应的音频文件,如果是多个文件则合并为一个文件
        audio_file_path = download_audio_files_and_merge(id, audio_origin_urls)

        
        # 更新任务状态
        self.update_state(
            state='PROGRESS',
            meta={'status': '开始转写', 'file': os.path.basename(audio_file_path)}
        )
        
        # 执行转写
        full_result = process_single_file(audio_file_path)
        
        if full_result:
            # 上传完整结果到OSS
            result_url = upload_result_to_oss(full_result, id)
            
            # 创建简化的结果，只包含基本信息和OSS URL
            simplified_result = {
                "file_name": full_result.get("file_name"),
                "file_size_mb": full_result.get("file_size_mb"),
                "statistics": full_result.get("statistics", {}),
                "speaker_segments_count": len(full_result.get("speaker_segments", [])),
                "speakers": list(set([s.get('speaker', 'unknown') for s in full_result.get("speaker_segments", [])])),
                "result_url": result_url,
                "completed_at": beijing_now().isoformat(),
                "storage_type": "oss"
            }
            
            # 添加音频时长统计
            if full_result.get("speaker_segments"):
                max_end_time = max([s.get("end_time", 0) for s in full_result["speaker_segments"]])
                simplified_result["audio_duration_seconds"] = max_end_time
            
            # 更新任务状态为完成
            self.update_state(
                state='SUCCESS',
                meta={'status': '转写完成', 'result': simplified_result}
            )
            
            print(f"✅ 任务完成，结果已简化并存储到OSS")
            print(f"   - 原始数据大小: {len(json.dumps(full_result, ensure_ascii=False).encode('utf-8')) / 1024:.1f}KB")
            print(f"   - 简化数据大小: {len(json.dumps(simplified_result, ensure_ascii=False).encode('utf-8')) / 1024:.1f}KB") 
            
            return simplified_result
        else:
            raise Exception("转写失败")
            
    except Exception as e:
        # 更新任务状态为失败
        self.update_state(
            state='FAILURE',
            meta={'status': '转写失败', 'error': str(e)}
        )
        raise

