import asyncio
import json
import threading
import time
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from openai import OpenAI
import requests
import logging

from app.models import Meeting, beijing_now
from app.mysql import get_db
from app.utils import group_segments_by_speaker_and_size
from app.tasks.executor import upload_result_to_oss
from config import cfg

logger = logging.getLogger(__name__)


class TaskManager(ABC):
    """通用任务管理器基类"""
    
    def __init__(self, max_workers: int = 5, check_interval: int = 5):
        self.is_running = False
        self.worker_thread = None
        self.lock = threading.Lock()
        self.running_tasks: Dict[int, threading.Thread] = {}  # task_id -> thread
        self.task_status: Dict[int, dict] = {}  # task_id -> status info
        self.max_workers = max_workers  # 最大并发任务数
        self.check_interval = check_interval  # 工作循环检查间隔（秒）
    
    def start(self):
        """启动任务管理器"""
        with self.lock:
            if self.is_running:
                return
            
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
            logger.info(f"{self.__class__.__name__} 任务管理器已启动")
            
        # 恢复未完成的任务
        self._recover_pending_tasks()

    def stop(self):
        """停止任务管理器"""
        with self.lock:
            if not self.is_running:
                return
            
            self.is_running = False
            
            # 等待所有运行中的任务完成
            for task_id, thread in list(self.running_tasks.items()):
                if thread.is_alive():
                    thread.join(timeout=5)  # 最多等待5秒
            
            logger.info(f"{self.__class__.__name__} 任务管理器已停止")

    def submit_task(self, task_id: int) -> bool:
        """提交任务"""
        with self.lock:
            if task_id in self.running_tasks:
                logger.warning(f"任务 {task_id} 已在运行中")
                return False
            
            if len(self.running_tasks) >= self.max_workers:
                logger.warning(f"已达到最大并发任务数 {self.max_workers}，任务 {task_id} 将排队等待")
                return False
            
            try:
                # 更新任务状态到数据库
                if not self._update_task_status_in_db(task_id, self._get_running_status()):
                    return False
                
                # 创建任务线程
                task_thread = threading.Thread(
                    target=self._execute_task_wrapper,
                    args=(task_id,),
                    daemon=True
                )
                
                self.running_tasks[task_id] = task_thread
                self.task_status[task_id] = {
                    "status": "starting",
                    "progress": 0,
                    "started_at": beijing_now().isoformat(),
                    "error": None
                }
                
                task_thread.start()
                logger.info(f"已提交任务 {task_id}")
                return True
                
            except Exception as e:
                logger.error(f"提交任务失败: {e}")
                return False

    def get_task_status(self, task_id: int) -> Optional[dict]:
        """获取任务状态"""
        with self.lock:
            return self.task_status.get(task_id)

    def cancel_task(self, task_id: int) -> bool:
        """取消任务"""
        with self.lock:
            if task_id not in self.running_tasks:
                return False
            
            # 更新状态为取消
            if task_id in self.task_status:
                self.task_status[task_id]["status"] = "canceled"
            
            # 更新数据库
            self._update_task_status_in_db(task_id, self._get_canceled_status())
            return True

    def _worker_loop(self):
        """工作线程主循环"""
        while self.is_running:
            try:
                # 清理已完成的任务
                self._cleanup_finished_tasks()
                
                # 检查是否有排队的任务需要启动
                self._start_pending_tasks()
                
                time.sleep(self.check_interval)  # 使用配置的检查间隔
                
            except Exception as e:
                logger.error(f"任务管理器工作循环出错: {e}")
                time.sleep(10)

    def _cleanup_finished_tasks(self):
        """清理已完成的任务"""
        with self.lock:
            finished_tasks = []
            for task_id, thread in self.running_tasks.items():
                if not thread.is_alive():
                    finished_tasks.append(task_id)
            
            for task_id in finished_tasks:
                del self.running_tasks[task_id]
                # 保留状态信息一段时间，供查询使用
                if task_id in self.task_status:
                    status = self.task_status[task_id]
                    if status.get("status") not in ["completed", "failed", "canceled"]:
                        status["status"] = "completed"

    def _start_pending_tasks(self):
        """启动排队中的任务"""
        if len(self.running_tasks) >= self.max_workers:
            return
            
        try:
            pending_task_ids = self._get_pending_tasks(self.max_workers - len(self.running_tasks))
            
            for task_id in pending_task_ids:
                if task_id not in self.running_tasks:
                    self.submit_task(task_id)
            
        except Exception as e:
            logger.error(f"启动排队任务失败: {e}")

    def _execute_task_wrapper(self, task_id: int):
        """任务执行包装器"""
        try:
            # 更新任务状态
            if task_id in self.task_status:
                self.task_status[task_id]["status"] = "running"
            
            # 执行具体任务
            self._execute_task(task_id)
            
            # 更新为完成状态
            if task_id in self.task_status:
                self.task_status[task_id].update({
                    "status": "completed",
                    "progress": 100,
                    "completed_at": beijing_now().isoformat()
                })
            
        except Exception as e:
            logger.error(f"执行任务 {task_id} 失败: {e}")
            
            # 更新任务状态为失败
            if task_id in self.task_status:
                self.task_status[task_id].update({
                    "status": "failed",
                    "error": str(e),
                    "failed_at": beijing_now().isoformat()
                })
            
            # 更新数据库状态
            self._update_task_status_in_db(task_id, self._get_failed_status())
            
        finally:
            # 从运行中任务列表移除
            with self.lock:
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]

    # 抽象方法，子类必须实现
    @abstractmethod
    def _execute_task(self, task_id: int):
        """执行具体任务 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _get_pending_tasks(self, limit: int) -> List[int]:
        """获取待处理的任务ID列表 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _recover_pending_tasks(self):
        """恢复未完成的任务 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _update_task_status_in_db(self, task_id: int, status: str) -> bool:
        """更新数据库中的任务状态 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _get_running_status(self) -> str:
        """获取运行中状态值 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _get_canceled_status(self) -> str:
        """获取取消状态值 - 子类需要实现"""
        pass
    
    @abstractmethod
    def _get_failed_status(self) -> str:
        """获取失败状态值 - 子类需要实现"""
        pass




class RefineTaskManager(TaskManager):
    """AI优化任务管理器"""
    
    def __init__(self, check_interval: int = 5):
        super().__init__(max_workers=5, check_interval=check_interval)
        
        # 获取AI优化的系统提示词
        try:
            response = requests.get(cfg.LLM.REFINE_PROMPT_URL)
            response.encoding = 'utf-8'
            self.refine_system_prompt = response.text
        except Exception as e:
            logger.error(f"获取AI优化提示词失败: {e}")
            self.refine_system_prompt = "请优化以下文本内容"

    def submit_refine_task(self, meeting_id: int) -> bool:
        """提交AI优化任务"""
        return self.submit_task(meeting_id)

    def _execute_task(self, meeting_id: int):
        """执行AI优化任务"""
        db = None
        try:
            logger.info(f"开始执行会议 {meeting_id} 的AI优化任务")
            
            db = next(get_db())
            meeting = db.query(Meeting).filter(Meeting.id == meeting_id).first()
            
            if not meeting:
                raise Exception(f"会议 {meeting_id} 不存在")
            
            if not meeting.textUrl:
                raise Exception("转写结果不存在")
            
            # 下载转写结果
            response = requests.get(meeting.textUrl, timeout=30)
            response.raise_for_status()
            
            try:
                text_content = json.loads(response.text)
            except json.JSONDecodeError:
                raise Exception("转写结果格式错误")
            
            # 准备分段数据
            segments = []
            for i, segment in enumerate(text_content.get("speaker_segments", [])):
                segments.append({
                    "id": i,
                    "speaker": segment["speaker"],
                    "start_time": segment["start_time"],
                    "end_time": segment["end_time"],
                    "text": segment["text"]
                })
            
            if len(segments) == 0:
                raise Exception("转写结果为空")
            
            # 按speaker和文本长度分组
            groups = group_segments_by_speaker_and_size(segments)
            
            # 更新任务状态
            if meeting_id in self.task_status:
                self.task_status[meeting_id].update({
                    "total_groups": len(groups),
                    "status": "processing",
                    "current_group": 0
                })
            
            logger.info(f"会议 {meeting_id} 分为 {len(groups)} 个组进行处理")
            
            # 使用线程池处理所有组
            max_workers = min(len(groups), 10)
            
            def process_group(group_data):
                group, group_index = group_data
                try:
                    result = self._process_single_group(group)
                    
                    # 更新进度
                    with self.lock:
                        if meeting_id in self.task_status:
                            self.task_status[meeting_id]["current_group"] = group_index + 1
                            self.task_status[meeting_id]["progress"] = int(
                                (group_index + 1) / len(groups) * 100
                            )
                    
                    return result
                except Exception as e:
                    logger.error(f"处理组 {group_index} 失败: {e}")
                    return group  # 返回原始数据作为fallback
            
            # 执行并发处理
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                group_data_list = [(group, i) for i, group in enumerate(groups)]
                results = list(executor.map(process_group, group_data_list))
            
            # 合并结果
            text_content["speaker_segments"] = [
                item for sublist in results for item in sublist
            ]
            
            # 上传结果到OSS
            refined_url = upload_result_to_oss(text_content, meeting_id)
            
            # 更新数据库
            meeting.refinedTextUrl = refined_url
            meeting.status = "refined"
            meeting.updatedAt = beijing_now()
            db.commit()
            
            # 更新任务状态
            if meeting_id in self.task_status:
                self.task_status[meeting_id].update({
                    "refined_url": refined_url
                })
            
            logger.info(f"会议 {meeting_id} AI优化任务完成")
            
        finally:
            if db:
                db.close()

    def _get_pending_tasks(self, limit: int) -> List[int]:
        """获取待处理的任务ID列表"""
        try:
            db = next(get_db())
            # 查找状态为refining但没有在运行的任务
            pending_meetings = db.query(Meeting).filter(
                Meeting.status == "refining"
            ).limit(limit).all()
            
            result = [meeting.id for meeting in pending_meetings]
            
            # 如果还有空余，查找状态为texted的任务
            if len(result) < limit:
                remaining_limit = limit - len(result)
                pending_meetings = db.query(Meeting).filter(
                    Meeting.status == "texted"
                ).limit(remaining_limit).all()
                
                result.extend([meeting.id for meeting in pending_meetings])
            
            db.close()
            return result
            
        except Exception as e:
            logger.error(f"获取待处理任务失败: {e}")
            return []

    def _recover_pending_tasks(self):
        """恢复未完成的任务"""
        try:
            db = next(get_db())
            pending_meetings = db.query(Meeting).filter(
                Meeting.status == "refining"
            ).all()
            
            logger.info(f"发现 {len(pending_meetings)} 个未完成的AI优化任务，开始恢复")
            
            for meeting in pending_meetings:
                self.submit_task(meeting.id)
            
            db.close()
        except Exception as e:
            logger.error(f"恢复未完成任务失败: {e}")

    def _update_task_status_in_db(self, meeting_id: int, status: str) -> bool:
        """更新数据库中的任务状态"""
        try:
            db = next(get_db())
            meeting = db.query(Meeting).filter(Meeting.id == meeting_id).first()
            if not meeting:
                logger.error(f"会议 {meeting_id} 不存在")
                return False
            
            meeting.status = status
            meeting.updatedAt = beijing_now()
            db.commit()
            db.close()
            return True
            
        except Exception as e:
            logger.error(f"更新数据库状态失败: {e}")
            return False

    def _get_running_status(self) -> str:
        """获取运行中状态值"""
        return "refining"
    
    def _get_canceled_status(self) -> str:
        """获取取消状态值"""
        return "texted"  # 回到文本化完成状态
    
    def _get_failed_status(self) -> str:
        """获取失败状态值"""
        return "failed"

    def _process_single_group(self, group: List[dict]) -> List[dict]:
        """处理单个分组"""
        logger.info(f"开始处理分组-->{group[0]}")

        try:
            segments = []
            for segment in group:
                segments.append({
                    "id": segment["id"],
                    "text": segment["text"]
                })

            client = OpenAI(
                api_key=cfg.LLM.API_KEY,
                base_url=cfg.LLM.BASE_URL
            )
            
            completion = client.chat.completions.create(
                model="qwen3-235b-a22b",
                messages=[
                    {"role": "system", "content": self.refine_system_prompt},
                    {"role": "user", "content": json.dumps(segments, ensure_ascii=False)},
                ],
                max_tokens=8192,
                stream=False,
                extra_body={"enable_thinking": False},
            )

            message = completion.choices[0].message.content
            
            # 解析AI返回的结果
            modify_segments = self._extract_json_array(message)
            
            # 验证返回数据
            if not isinstance(modify_segments, list):
                raise ValueError("AI返回的数据不是数组格式")
            
            for i, seg in enumerate(modify_segments):
                if not isinstance(seg, dict):
                    raise ValueError(f"第{i+1}个元素不是对象格式")
                if "id" not in seg or "text" not in seg:
                    raise ValueError(f"第{i+1}个元素缺少必要字段(id或text)")
            
            # 构建修改映射
            modify_segMap = {}
            merged_ids = []
            merged_to = {}
            for modify_seg in modify_segments:
                modify_segMap[modify_seg["id"]] = modify_seg["text"]
                if "merged_ids" in modify_seg:
                    ids = modify_seg["merged_ids"]
                    merged_ids.extend(ids[1:])
                    for merged_id in ids[1:]:
                        merged_to[merged_id] = ids[0]

            # 生成输出结果
            output_segments = []
            group_map = {}
            for seg in group:
                group_map[seg["id"]] = seg

            for seg in group:
                if seg["id"] in merged_ids:
                    toItem = merged_to[seg["id"]]
                    currSeg = group_map[seg["id"]]
                    max_end_time = max(currSeg["end_time"], group_map[toItem]["end_time"])
                    group_map[toItem]["end_time"] = max_end_time
                    continue

                if seg["id"] not in modify_segMap:
                    output_segments.append(seg)
                    continue

                # 创建新的段落对象
                #new_seg = seg.copy()
                seg["text"] = modify_segMap[seg["id"]]
                output_segments.append(seg)
            
            return output_segments
            
        except Exception as e:
            logger.error(f"处理单个组时出错: {e}")
            return group  # 返回原始数据作为fallback

    def _extract_json_array(self, text: str) -> List[dict]:
        """从AI返回的文本中提取JSON数组"""
        if not text or not isinstance(text, str):
            raise ValueError("返回内容为空或格式错误")
        
        text = text.strip()
        
        # 处理markdown代码块格式
        if "```json" in text:
            start_markers = ["```json", "```JSON"]
            end_marker = "```"
            
            for start_marker in start_markers:
                if start_marker in text:
                    start_idx = text.find(start_marker) + len(start_marker)
                    end_idx = text.find(end_marker, start_idx)
                    if end_idx != -1:
                        text = text[start_idx:end_idx].strip()
                        break
        
        # 处理其他代码块格式
        elif "```" in text:
            parts = text.split("```")
            if len(parts) >= 3:
                text = parts[1].strip()
                lines = text.split('\n')
                if lines and lines[0].lower() in ['json', 'javascript', 'js']:
                    text = '\n'.join(lines[1:]).strip()
        
        # 查找JSON数组的开始和结束位置
        start_idx = -1
        for i, char in enumerate(text):
            if char == '[':
                start_idx = i
                break
        
        if start_idx == -1:
            for i, char in enumerate(text):
                if char == '{':
                    start_idx = i
                    break
        
        if start_idx == -1:
            raise ValueError("未找到JSON数据的开始位置")
        
        # 查找对应的结束位置
        bracket_count = 0
        start_char = text[start_idx]
        end_char = ']' if start_char == '[' else '}'
        
        for i in range(start_idx, len(text)):
            char = text[i]
            if char == start_char:
                bracket_count += 1
            elif char == end_char:
                bracket_count -= 1
                if bracket_count == 0:
                    end_idx = i
                    break
        else:
            raise ValueError("未找到JSON数据的结束位置")
        
        # 提取并解析JSON
        json_str = text[start_idx:end_idx + 1]
        
        try:
            parsed_data = json.loads(json_str)
            
            if isinstance(parsed_data, list):
                return parsed_data
            elif isinstance(parsed_data, dict):
                return [parsed_data]
            else:
                raise ValueError("解析出的数据不是对象或数组")
                
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON解析失败: {str(e)}")


# 全局任务管理器实例
refine_task_manager = RefineTaskManager() 