import json
import os
import tempfile
import requests
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, or_
from typing import List, Literal, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime

from app.models import Meeting, MeetingSummeryTask, MeetingTextRefineTask, HttpResult, success, error, TaskStatus
import logging
from app.mysql import get_db
from app.utils import upload_text_to_oss
from config import cfg
from app.models import Meeting
from app.services.docx import TextSegment, MeetingTextDocxParser
from app.services.summeryDocx import SummeryDocxParser
import oss2
import hashlib

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/api/export", tags=["导出接口"])


class ExportMeetingTextRequest(BaseModel):
    id: int
    isRefined: bool 


@router.post("/text", response_model=HttpResult[str], summary="导出会议文本")
async def export_meeting_text(
    req: ExportMeetingTextRequest,
    db: Session = Depends(get_db)
):
    """
    导出会议文本为DOCX文件

    Args:
        req: 导出请求，包含会议ID和是否使用微调后文本的标志
        db: 数据库会话

    Returns:
        HttpResult[str]: 包含生成的DOCX文件URL的响应
    """
    try:
        logger.info(f"开始导出会议 {req.id} 的文本，使用微调文本: {req.isRefined}")
        text_url = None
        title = ""

        # 1. 查询会议信息
        if not req.isRefined:
            meeting = db.query(Meeting).filter(Meeting.id == req.id).first()
            if not meeting:
                raise HTTPException(status_code=404, detail="会议不存在")
            
            text_url = meeting.textUrl
            title = meeting.name

        # 2. 获取文本数据URL
      
        if req.isRefined:

            # 使用微调后的文本
            refine_task = db.query(MeetingTextRefineTask).filter(
                MeetingTextRefineTask.id == req.id
            ).first()
            if not refine_task:
                raise HTTPException(status_code=404, detail="未找到微调后的文本结果")
            text_url = refine_task.resultUrl

            meeting = db.query(Meeting).filter(Meeting.id == refine_task.meeting_id).first()
            title = meeting.name
            
        if not text_url:
            raise HTTPException(status_code=404, detail="未找到文本结果")
        
        # 3. 下载并解析文本数据
        logger.info(f"正在下载文本数据: {text_url}")
        response = requests.get(text_url, timeout=30)
        response.raise_for_status()

        try:
            text_data = json.loads(response.text)
        except json.JSONDecodeError:
            raise HTTPException(status_code=500, detail="文本数据格式错误")

        # 4. 提取speaker_segments
        speaker_segments = text_data.get("speaker_segments", [])
        if not speaker_segments:
            raise HTTPException(status_code=404, detail="文本数据中未找到speaker_segments")

        # 5. 转换为TextSegment对象
        segments = []
        for segment in speaker_segments:
            segments.append(TextSegment(
                start_time=float(segment.get("start_time", 0)),
                end_time=float(segment.get("end_time", 0)),
                speaker=str(segment.get("speaker", "unknown")),
                text=str(segment.get("text", ""))
            ))

        # 6. 生成会议标题
        title = title or f"会议记录_{req.id}"

        # 7. 处理DOCX模板
        template_path = "static/text.docx"
        if not os.path.exists(template_path):
            raise HTTPException(status_code=500, detail="DOCX模板文件不存在")

        # 8. 使用DocxParser处理文档
        parser = MeetingTextDocxParser()

        # 读取模板
        document_xml = parser.read_docx_document(template_path)
        if document_xml is None:
            raise HTTPException(status_code=500, detail="无法读取DOCX模板")

        # 处理会议文本
        modified_xml = parser.saveMeetingText2docx(title, segments, document_xml)

        # 9. 生成临时文件
        with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as temp_file:
            temp_path = temp_file.name

        # 保存修改后的文档
        if not parser.save_docx_document(template_path, temp_path, modified_xml):
            raise HTTPException(status_code=500, detail="保存DOCX文件失败")

        # 10. 上传到OSS
        try:
            file_url = _upload_local_file_to_oss(temp_path, f"exported_docs/{req.id}")
            logger.info(f"DOCX文件已上传到OSS: {file_url}")

            return success(file_url)

        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出会议文本失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

class ExportSummeryRequest(BaseModel):
    id: int

@router.post("/summery", response_model=HttpResult[str], summary="导出会议纪要word文件")
async def export_meeting_summery(
    req: ExportSummeryRequest,
    db: Session = Depends(get_db)
):
    """
    导出会议纪要为DOCX文件

    Args:
        req: 导出请求，包含会议ID和是否使用微调后文本的标志
        db: 数据库会话

    Returns:
        HttpResult[str]: 包含生成的DOCX文件URL的响应
    """
    logger.info(f"开始导出Id= {req.id} 的纪要")
    text_url = None
    title = ""

    task = db.query(MeetingSummeryTask).filter(MeetingSummeryTask.id == req.id).first()
    if not task:
        raise HTTPException(status_code=404, detail="会议不存在")
    
    # 2. 获取文本数据URL
    text_url = task.resultUrl

    meeting = db.query(Meeting).filter(Meeting.id == task.meeting_id).first()
    title = meeting.name
        
    if not text_url:
        raise HTTPException(status_code=404, detail="未找到文本结果")


    # 3. 下载并解析文本数据
    logger.info(f"正在下载纪要数据: {text_url}")
    response = requests.get(text_url, timeout=30)
    response.raise_for_status()
    response.encoding = 'utf-8'
    
    markdown_text = response.text

    # 6. 生成会议标题
    title = title or f"会议纪要_{req.id}"

    # 7. 处理DOCX模板
    template_path = "static/summery.docx"
    if not os.path.exists(template_path):
        raise HTTPException(status_code=500, detail="纪要DOCX模板文件不存在")

    # 8. 使用DocxParser处理文档
    parser = SummeryDocxParser()

    # 读取模板
    document_xml = parser.read_docx_document(template_path)
    if document_xml is None:
        raise HTTPException(status_code=500, detail="无法读取DOCX模板")

    # 处理会议文本
    modified_xml = parser.saveSummeryMarkdown2docx(markdown_text, meeting, document_xml)

    # 9. 生成临时文件
    with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as temp_file:
        temp_path = temp_file.name

    # 保存修改后的文档
    if not parser.save_docx_document(template_path, temp_path, modified_xml):
        raise HTTPException(status_code=500, detail="保存DOCX文件失败")

    # 10. 上传到OSS
    try:
        file_url = _upload_local_file_to_oss(temp_path, f"summery_docs/{req.id}")
        logger.info(f"纪要文件已上传到OSS: {file_url}")

        return success(file_url)
    except Exception as e:
        logger.error(f"导出纪要失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.remove(temp_path)

def _upload_local_file_to_oss(local_file_path: str, prefix: str) -> str:
    """
    上传本地文件到OSS

    Args:
        local_file_path: 本地文件路径
        prefix: OSS存储前缀

    Returns:
        str: OSS文件URL
    """
    try:
        # 读取本地文件
        with open(local_file_path, 'rb') as f:
            file_data = f.read()

        # 生成文件哈希和名称
        file_hash = hashlib.md5(file_data).hexdigest()
        file_extension = os.path.splitext(local_file_path)[1]
        file_name = f"{file_hash}{file_extension}"

        # 配置OSS
        auth = oss2.Auth(cfg.OSS.ACCESS_KEY_ID, cfg.OSS.ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, cfg.OSS.ENDPOINT, cfg.OSS.BUCKET_NAME)

        # 生成文件路径
        timestamp = int(datetime.now().timestamp() * 1000)
        filename = f"{prefix}/{datetime.now().year}/{datetime.now().month:02d}/{timestamp}_{file_name}"

        # 上传到OSS
        bucket.put_object(filename, file_data, headers={'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'})

        # 生成文件URL
        file_url = f"https://{cfg.OSS.BUCKET_NAME}.{cfg.OSS.ENDPOINT}/{filename}"

        logger.info(f"✅ 文件已上传到OSS: {file_url}")
        return file_url

    except Exception as e:
        logger.error(f"❌ 上传文件到OSS失败: {e}")
        raise