from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime, timezone, timedelta
from enum import Enum as PyEnum

from pydantic import BaseModel
from typing import Generic, TypeVar, Optional
from app.mysql import pwd_context

T = TypeVar('T')  # 泛型类型

# 创建东八区时间函数 - 更健壮的实现
def beijing_now():
    """返回东八区（北京时间）的当前时间，不依赖系统时区设置"""
    # 获取UTC时间
    utc_now = datetime.utcnow()
    # 创建东八区时区
    beijing_tz = timezone(timedelta(hours=8))
    # 转换为东八区时间并移除时区信息（用于数据库存储）
    return utc_now.replace(tzinfo=timezone.utc).astimezone(beijing_tz).replace(tzinfo=None)

# 或者使用更简单的方式（如果确定服务器时区正确）
def beijing_now_simple():
    """简单版本：直接返回当前时间（假设服务器时区已正确设置为东八区）"""
    return datetime.now()

class HttpResult(BaseModel, Generic[T]):
    code: int = 200    # 状态码
    message: str       # 返回消息
    data: Optional[T]  # 实际数据（可选）

def success(data: T) -> HttpResult[T]:
    return HttpResult(
        code=200,
        message="",
        data=data
    )

def error(message: str, code:int=500) -> HttpResult[None]:
    return HttpResult(
        code=code,
        message=message,
        data=None
    )


# 创建基础类
Base = declarative_base()


class User(Base):
    __tablename__ = "user"
    
    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False)
    password = Column(String(200), nullable=False)
    
    # 关系
    conversations = relationship("Conversation", back_populates="user", cascade="all, delete-orphan")
    
    def set_password(self, password):
        self.password = pwd_context.hash(password)
    
    def check_password(self, password):
        return pwd_context.verify(password, self.password)

class Conversation(Base):
    __tablename__ = "conversation"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False, index=True)
    title = Column(String(255), nullable=False, default="新对话")
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)
    
    # 关系
    user = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_user_updated", user_id, updated_at.desc()),
    )

class Message(Base):
    __tablename__ = "message"
    
    id = Column(Integer, primary_key=True)
    conversation_id = Column(Integer, ForeignKey("conversation.id"), nullable=False, index=True)
    role = Column(Enum("user", "assistant"), nullable=False)
    content = Column(Text, nullable=False)
    reasoning_content = Column(Text)
    created_at = Column(DateTime, default=beijing_now)
    updated_at = Column(DateTime, default=beijing_now, onupdate=beijing_now)  # 新增字段
    
    # 关系
    conversation = relationship("Conversation", back_populates="messages")
    
    # 索引
    __table_args__ = (
        Index("idx_conv_created", conversation_id, created_at),
    )

class Meeting(Base):
    __tablename__ = "meetings"
    
    id = Column(Integer, primary_key=True)

    name = Column(String(80), nullable=False)
    meetType = Column(String(80))
    users = Column(Text)
    desc = Column(Text)
    user_id = Column(Integer, nullable=False, index=True)

    meetingAddress = Column(String(255))
    meetingAt = Column(DateTime, default=beijing_now) #会议时间
    createdAt = Column(DateTime, default=beijing_now)
    updatedAt = Column(DateTime, default=beijing_now, onupdate=beijing_now)  # 新增字段
    
    audioOriginUrls = Column(Text, nullable=False)
    audioOriginSize = Column(Integer) #会议大小
    
    audioDuration = Column(Integer) #会议时长
    audioUrl = Column(String(255))
    audioSize = Column(Integer) #会议大小
    audioState = Column(Enum("uploaded", "handing", "finished", "failed"), nullable=False) #会议状态
    summeryState = Column(Enum("waiting", "started", "success", "failed"), nullable=False) #摘要状态

    asrTaskId = Column(String(255)) #音频转文本任务id
    summeryTaskId = Column(String(255)) #摘要任务id

    spkList = Column(Text) #说话人列表
    textUrl = Column(String(255)) #文本结果URL
    refinedTextUrl = Column(String(255)) #Ai微调后文本结果URL
    summaryUrl = Column(String(255)) #摘要结果URL


  

class MeetingRefineTask(Base):
    __tablename__ = "meeting_refine_tasks"
    id = Column(Integer, primary_key=True)
    meeting_id = Column(Integer, ForeignKey("meetings.id"), nullable=False, index=True)
    user_id = Column(Integer, nullable=False, index=True)

    versionName = Column(String(32)) #版本
    textUrl = Column(String(255)) #文本URL
    refTextUrl = Column(String(255)) #

    textRefinedUrl = Column(String(255)) #文本微调后URL
    summeryRefinedUrl = Column(String(255)) #摘要微调后URL

    textTaskId = Column(String(255)) #文本任务id
    summeryTaskId = Column(String(255)) #摘要任务id

    textRefineState = Column(Enum("waiting", "started", "success", "failed"), nullable=False) #文本微调状态
    summeryRefineState = Column(Enum("waiting", "started", "success", "failed"), nullable=False) #摘要微调状态

    createdAt = Column(DateTime, default=beijing_now)
    updatedAt = Column(DateTime, default=beijing_now, onupdate=beijing_now) # 新增字段



class MeetingTextRefineTask(Base):
    __tablename__ = "meeting_text_refine_tasks"
    
    id = Column(Integer, primary_key=True)
    versionName = Column(String(32)) #版本
    meeting_id = Column(Integer, ForeignKey("meetings.id"), nullable=False, index=True)
    textUrl = Column(String(255)) #文本URL
    refTextUrl = Column(String(255)) #
    resultUrl = Column(String(255)) #Ai微调后文本结果URL
    status = Column(Enum("created","started", "success", "failed", "canceled"), nullable=False) #处理状态
    createdAt = Column(DateTime, default=beijing_now)
    updatedAt = Column(DateTime, default=beijing_now, onupdate=beijing_now) # 新增字段
    
    taskId = Column(String(255))

class MeetingSummeryTask(Base):
    __tablename__ = "meeting_summery_tasks"
    id = Column(Integer, primary_key=True)
    versionName = Column(String(32)) #版本
    meeting_id = Column(Integer, ForeignKey("meetings.id"), nullable=False, index=True)
    textUrl = Column(String(255)) #文本L
    refTextUrl = Column(String(255)) #Ai微调后文本结果URL
    resultUrl = Column(String(255)) #Ai微调后文本结果URL
    status = Column(Enum("created","started", "success", "failed"), nullable=False) #处理状态
    createdAt = Column(DateTime, default=beijing_now)
    updatedAt = Column(DateTime, default=beijing_now, onupdate=beijing_now) # 新增字段
    
    taskId = Column(String(255))

class LoginToken(BaseModel):
    token: str      # 状态码
    username: str   # 返回消息

class UserProfile(BaseModel):
    username: str   # 返回消息


# 定义一个字符串的枚举
class TaskStatus(PyEnum):
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
