from celery import Celery
from config import cfg
import threading
import logging
from datetime import datetime
from celery.events import EventReceiver
from celery.result import AsyncResult
import json

from app.models import beijing_now

# 设置日志
logger = logging.getLogger(__name__)

# 创建 Celery 应用实例 - 监听 Worker
monitor_app = Celery(
    'meeting_api_monitor',
    broker=cfg.CELERY.BROKER_URL,
    backend=cfg.CELERY.RESULT_BACKEND,
    include=['app.tasks.monitor']
)

# 配置 Celery
monitor_app.conf.update(
    task_serializer=cfg.CELERY.TASK_SERIALIZER,
    result_serializer=cfg.CELERY.RESULT_SERIALIZER,
    accept_content=cfg.CELERY.ACCEPT_CONTENT,
    timezone=cfg.CELERY.TIMEZONE,
    enable_utc=cfg.CELERY.ENABLE_UTC,
    task_track_started=cfg.CELERY.TASK_TRACK_STARTED,
    task_time_limit=cfg.CELERY.TASK_TIME_LIMIT,
    task_soft_time_limit=cfg.CELERY.TASK_SOFT_TIME_LIMIT,
)

# 配置定时任务 - 监听 Worker 的定时任务
monitor_app.conf.beat_schedule = {
    'monitor-pending-tasks': {
        'task': 'monitor_pending_tasks',
        'schedule': 30.0,  # 每30秒检查一次待处理任务
    }
}


def handle_event(event):
    event_type = event['type']
    task_id = event.get('uuid')
    task_name = event.get('name')
    state = None
    result = None
    args = event.get('args')
    kwargs = event.get('kwargs')
    exception = event.get('exception')

    if event_type == 'worker-heartbeat':
        return
    
    print(f"event_type: {event_type}")

    if event_type == 'task-started':
        state = 'STARTED'
        logger.info(f"任务开始: {task_name}, ID: {task_id}")
        update_task_status_in_db(task_id, state)
    elif event_type == 'task-succeeded':
        state = 'SUCCESS'
        result = event.get('result')
        logger.info(f"任务成功: {task_name}, ID: {task_id}")
        update_task_status_in_db(task_id, state, result)
        if task_name == 'transcribe_audio':
            handle_transcribe_success(task_id, result)
    elif event_type == 'task-failed':
        state = 'FAILURE'
        logger.info(f"任务失败: {task_name}, ID: {task_id}")
        update_task_status_in_db(task_id, state, str(exception))
        if task_name == 'transcribe_audio':
            handle_transcribe_failure(task_id, exception, args)
    
    elif event_type == 'task-revoked':
        state = 'REVOKED'
        logger.info(f"任务撤销: {task_name}, ID: {task_id}")
        update_task_status_in_db(task_id, state)

def start_event_monitor():
    def _run():
        with monitor_app.connection() as connection:
            recv = EventReceiver(connection, handlers={'*': handle_event}, app=monitor_app)
            logger.info("全局事件监听已启动")
            recv.capture(limit=None, timeout=None, wakeup=True)
    t = threading.Thread(target=_run, daemon=True)
    t.start()

@monitor_app.on_after_configure.connect
def setup_monitor_tasks(sender, **kwargs):
    """监听 Worker 启动时的配置函数"""
    print("🔍 Celery 监听 Worker 配置完成")
    start_event_monitor()


def handle_transcribe_success(task_id: str, result: dict):
    """处理音频转写任务成功 - 业务逻辑处理"""
    try:
        from app.mysql import get_db
        from app.models import Meeting
        
        db = next(get_db())
        
        # 查找对应的会议记录
        meeting = db.query(Meeting).filter(Meeting.celeryTaskId == task_id).first()
        
        if meeting:
            # 处理业务逻辑：保存转写结果到专门的表
            if result and isinstance(result, dict):
                # 这里可以保存转写结果到专门的表
                # 例如：保存到 meeting_transcriptions 表
                logger.info(f"会议 {meeting.id} 转写结果处理完成")
            
            # 注意：不在这里更新数据库状态，由 task_postrun 统一处理
            logger.info(f"会议 {meeting.id} 音频转写业务逻辑处理完成")
        else:
            logger.warning(f"未找到任务ID为 {task_id} 的会议记录")
            
    except Exception as e:
        logger.error(f"处理音频转写成功回调时发生错误: {str(e)}")
    finally:
        try:
            db.close()
        except:
            pass

def handle_transcribe_failure(task_id: str, exception: Exception, args: tuple):
    """处理音频转写任务失败 - 业务逻辑处理"""
    try:
        from app.mysql import get_db
        from app.models import Meeting
        
        db = next(get_db())
        
        # 查找对应的会议记录
        meeting = db.query(Meeting).filter(Meeting.celeryTaskId == task_id).first()
        
        if meeting:
            # 处理业务逻辑：记录失败原因、发送告警等
            error_info = {
                "error": str(exception),
                "error_type": type(exception).__name__,
                "args": str(args) if args else None
            }
            
            # 这里可以添加失败处理逻辑，如发送告警邮件、记录详细日志等
            logger.error(f"会议 {meeting.id} 转写失败，错误详情: {error_info}")
            
            # 注意：不在这里更新数据库状态，由 task_postrun 统一处理
            logger.info(f"会议 {meeting.id} 音频转写失败业务逻辑处理完成")
        else:
            logger.warning(f"未找到任务ID为 {task_id} 的会议记录")
            
    except Exception as e:
        logger.error(f"处理音频转写失败回调时发生错误: {str(e)}")
    finally:
        try:
            db.close()
        except:
            pass

def update_task_status_in_db(task_id: str, state: str, result=None):
    """更新数据库中的任务状态 - 通用状态更新"""
    try:
        from app.mysql import get_db
        from app.models import Meeting
        
        db = next(get_db())
        
        # 查找对应的会议记录
        meeting = db.query(Meeting).filter(Meeting.celeryTaskId == task_id).first()
        
        if meeting:
            # 根据状态更新数据库字段
            if state == 'PENDING':
                meeting.audioState = "uploaded"  # 保持为已上传状态
                meeting.celeryTaskStatus = "pending"
                if result:
                    meeting.celeryTaskResult = json.dumps(result, ensure_ascii=False)
            elif state == 'STARTED':
                meeting.audioState = "handing"
                meeting.celeryTaskStatus = "running"
                if result:
                    meeting.celeryTaskResult = json.dumps(result, ensure_ascii=False)
            elif state == 'SUCCESS':
                meeting.audioState = "finished"
                meeting.celeryTaskStatus = "finished"
                if result:
                    meeting.celeryTaskResult = json.dumps(result, ensure_ascii=False)
            elif state == 'FAILURE':
                meeting.audioState = "failed"
                meeting.celeryTaskStatus = "failed"
                if result:
                    meeting.celeryTaskResult = json.dumps(result, ensure_ascii=False)
            elif state == 'REVOKED':
                meeting.audioState = "canceled"
                meeting.celeryTaskStatus = "canceled"
            
            meeting.updatedAt = beijing_now()
            db.commit()
            
            logger.info(f"任务 {task_id} 状态已更新为: {state}")
        else:
            logger.warning(f"未找到任务ID为 {task_id} 的会议记录")
            
    except Exception as e:
        logger.error(f"更新任务状态时发生错误: {str(e)}")
    finally:
        try:
            db.close()
        except:
            pass 



@monitor_app.task
def monitor_pending_tasks():
    """监控待处理的任务状态"""
    try:
        from app.mysql import get_db
        from app.models import Meeting
        
        db = next(get_db())
        
        # 查询状态为 pending 或 started 的任务
        pending_meetings = db.query(Meeting).filter(
            Meeting.celeryTaskStatus.in_(['pending', 'started'])
        ).all()
        
        updated_count = 0
        for meeting in pending_meetings:
            if meeting.celeryTaskId:
                result = AsyncResult(meeting.celeryTaskId)
                if result.ready():
                    # 任务已完成，更新状态
                    if result.successful():
                        update_task_status_in_db(meeting.celeryTaskId, 'SUCCESS', result.result)
                    else:
                        update_task_status_in_db(meeting.celeryTaskId, 'FAILURE', str(result.info))
                    updated_count += 1
        
        if updated_count > 0:
            logger.info(f"监控更新了 {updated_count} 个任务状态")
        
        db.close()
        return f"监控完成，更新了 {updated_count} 个任务"
        
    except Exception as e:
        logger.error(f"监控待处理任务时发生错误: {str(e)}")
        return f"监控失败: {str(e)}"